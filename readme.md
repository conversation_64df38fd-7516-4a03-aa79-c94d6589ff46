# 🎯 Code Defense
*A Programmable Military Systems Simulation Game*

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Engine: Babylon.js](https://img.shields.io/badge/Engine-Babylon.js-blue)](https://www.babylonjs.com/)
[![Language: JavaScript](https://img.shields.io/badge/Language-JavaScript-yellow)](https://developer.mozilla.org/en-US/docs/Web/JavaScript)

## 📋 Overview

**Code Defense** is an innovative educational game that combines programming education with military simulation. Players learn real programming concepts by writing code to control various weapons systems, from basic turrets to advanced guided missile systems, while defending against increasingly sophisticated threats.

Unlike traditional tower defense games, players don't just place and upgrade towers—they **program** them. Write JavaScript/Lua code to control turret aiming, missile guidance, radar systems, and defensive coordination.

## 🎮 Game Concept

### Core Gameplay Loop
1. **Analyze the Threat**: Study incoming enemies using radar and sensor data
2. **Write Code**: Program your weapons systems to respond automatically
3. **Deploy & Test**: Watch your code in action as enemies attack
4. **Iterate & Improve**: Debug and optimize your defensive algorithms

### Educational Focus
- **Programming Fundamentals**: Variables, functions, loops, conditionals
- **Mathematics**: Trigonometry, ballistics, vector math, numerical methods  
- **Physics**: Projectile motion, guidance systems, sensor limitations
- **Systems Engineering**: Multi-system coordination, resource allocation
- **Military Tactics**: Threat assessment, engagement doctrine, defensive strategy

## 🚀 Features

### Current Implementation (v0.1)
- ✅ 3D Babylon.js environment with realistic physics
- ✅ Programmable turret system with elevation/azimuth control
- ✅ Basic enemy AI and spawning system
- ✅ Ballistic projectile simulation with gravity
- ✅ Real-time code execution and debugging
- ✅ Collision detection and explosion effects

### Planned Features
- 🔄 **Guided Munitions**: Heat-seeking, radar-guided, and command-guided missiles
- 🔄 **Advanced Radar**: Multiple radar types, scan patterns, electronic warfare
- 🔄 **Defensive Systems**: Interceptor missiles, point defense guns, layered defense
- 🔄 **Campaign Mode**: Progressive difficulty with 18+ levels
- 🔄 **Code Editor**: Syntax highlighting, auto-completion, debugging tools
- 🔄 **Multiplayer**: Cooperative defense scenarios

## 🎯 Game Progression

### Phase 1: Direct Fire Weapons (Levels 1-6)
- **Level 1**: Basic turret control with manual elevation/azimuth
- **Level 2**: Moving target engagement and predictive targeting
- **Level 3**: Artillery with ballistic arcs and gravity
- **Level 4**: Air resistance and numerical solution methods
- **Level 5**: Multi-target management and priority systems
- **Level 6**: Guided ballistic shells with course correction

### Phase 2: Guided Munitions (Levels 7-12)
- **Level 7**: Heat-seeking missiles with automatic target acquisition
- **Level 8**: Command-guided missiles with manual steering
- **Level 9**: Active radar missiles with target discrimination
- **Level 10**: Multi-stage missiles with booster separation
- **Level 11**: Swarm munitions with distributed coordination
- **Level 12**: Loitering munitions and autonomous patrol

### Phase 3: Defensive Systems (Levels 13-18)
- **Level 13**: Point defense guns against incoming projectiles
- **Level 14**: Interceptor missiles for missile-vs-missile combat
- **Level 15**: Layered defense coordination and system integration
- **Level 16**: Counter-battery fire and rapid response systems
- **Level 17**: Electronic warfare and countermeasures
- **Level 18**: Integrated air defense and battle management

## 🛠️ Technical Architecture

### Technology Stack
- **Engine**: Babylon.js 6.0+ for 3D rendering and physics
- **Scripting**: JavaScript for game logic, Lua integration planned for user code
- **Physics**: Cannon.js physics engine
- **Platform**: Web-based (HTML5), desktop versions planned

### Code Architecture
```
src/
├── core/
│   ├── Game.js              # Main game class
│   ├── Scene.js             # 3D scene management
│   └── Physics.js           # Ballistics and collision systems
├── weapons/
│   ├── Turret.js            # Basic turret implementation
│   ├── Missile.js           # Guided munition systems
│   └── Artillery.js         # Ballistic weapon systems
├── radar/
│   ├── RadarSystem.js       # Sensor simulation
│   └── TargetTracking.js    # Track correlation and fusion
├── enemies/
│   ├── EnemyAI.js           # Enemy behavior and movement
│   └── ThreatGeneration.js  # Dynamic threat spawning
└── ui/
    ├── CodeEditor.js        # Programming interface
    └── TacticalDisplay.js   # Radar and status displays
```

## 🚀 Getting Started

### Prerequisites
- Modern web browser with WebGL 2.0 support
- Node.js 16+ (for development)
- Basic understanding of JavaScript

### Installation
```bash
# Clone the repository
git clone https://github.com/yourusername/code-defense.git
cd code-defense

# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build
```

