<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Babylon.js with TypeScript</title>
    <style>
      html, body {
        margin: 0;
        overflow: hidden;
        width: 100%;
        height: 100%;
      }
      .screen_container {
        display: flex;
        flex-direction: row;
        width: 100%;
        height: 100%;
      }
      canvas {
        width: 50%;
        height: 100%;
        display: block;
      }
      #vs_container {
        height: 80%;
        display: block;
        border-bottom: 1px solid black;
      }
      .sidebar {
        width: 50%;
        height: 100%;
        display: flex;
        flex-direction: column
      }
    </style>
  </head>
  <body>
    <div class="screen_container">
        <div class="sidebar">
            <div id="vs_container"></div>
            <button id="deploy_code">Deploy</button>
        </div>
        <canvas id="renderCanvas"></canvas>
    </div>
    <script type="module" src="/src/main.ts"></script>
    <script src="../node_modules/monaco-editor/min/vs/loader.js"></script>
    <script>
        require.config({
            paths: { vs: "https://cdn.jsdelivr.net/npm/monaco-editor@0.27.0/min/vs" }
        });
        require.config({ paths: { vs: '../node_modules/monaco-editor/min/vs' } });
        
        const baseCode = ['\tconsole.log("hello!")'].join('\n');

        require(['vs/editor/editor.main'], function () {
            var editor = monaco.editor.create(document.getElementById('vs_container'), {
                value: [baseCode].join('\n'),
                language: 'javascript'
            });

            window.userCode = baseCode;

            editor.getModel().onDidChangeContent(() => {
                window.userCode = editor.getValue();
            });
        });
    </script>
  </body>
</html>
