import { type RadarContact } from "./game/radar";

type RadarDisplayOptions = {
    range?: number;
    rpm?: number;
    fadeTime?: number;
    sweepWidth?: number;
    trailLength?: number;
    showInfo?: boolean;
    backgroundColor?: string;
    primaryColor?: string;
    contactColor?: string;
}

class RadarDisplay {
    private container: HTMLElement;
    private canvas: HTMLCanvasElement;

    private options: RadarDisplayOptions;

    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        if (!this.container) {
            throw new Error(`Container with id '${containerId}' not found`);
        }
        
        // Default options
        this.options = {
            range: 50,
            rpm: 30,
            fadeTime: 10000,
            sweepWidth: 60,
            trailLength: 8,
            showInfo: false,
            backgroundColor: '#000',
            primaryColor: '#00ff00',
            contactColor: '#ffff00',
            ...options
        };
        
        this.contacts = new Map();
        this.beamAngle = 0;
        this.animationId = null;
        
        this.initializeComponent();
        this.setupResizeObserver();
        this.startAnimation();
    }
    
    initializeComponent() {
        // Create canvas element
        this.canvas = document.createElement('canvas');
        this.ctx = this.canvas.getContext('2d');
        
        // Style the canvas
        // this.canvas.style.width = '100%';
        this.canvas.style.height = '100%';
        this.canvas.style.background = this.options.backgroundColor;
        this.canvas.style.borderRadius = '50%';
        this.canvas.style.border = `1px solid ${this.options.primaryColor}`;
        
        // Clear container and add canvas
        this.container.innerHTML = '';
        this.container.style.position = 'relative';
        this.container.appendChild(this.canvas);
        
        // Create info panel if requested
        if (this.options.showInfo) {
            this.createInfoPanel();
        }
        
        this.resize();
    }
    
    createInfoPanel() {
        this.infoPanel = document.createElement('div');
        this.infoPanel.style.cssText = `
            position: absolute;
            top: 10px;
            right: 10px;
            color: ${this.options.primaryColor};
            font-family: 'Courier New', monospace;
            font-size: 12px;
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            border: 1px solid ${this.options.primaryColor};
            border-radius: 4px;
            pointer-events: none;
        `;
        this.container.appendChild(this.infoPanel);
    }
    
    updateInfoPanel() {
        if (this.infoPanel) {
            this.infoPanel.innerHTML = `
                <div>RANGE: ${this.options.range} NM</div>
                <div>RPM: ${this.options.rpm}</div>
                <div>CONTACTS: ${this.contacts.size}</div>
                <div>BEAM: ${Math.round(this.beamAngle)}°</div>
            `;
        }
    }
    
    setupResizeObserver() {
        if (window.ResizeObserver) {
            this.resizeObserver = new ResizeObserver(() => {
                this.resize();
            });
            this.resizeObserver.observe(this.container);
        } else {
            // Fallback for older browsers
            window.addEventListener('resize', () => this.resize());
        }
    }
    
    resize() {
        const rect = this.container.getBoundingClientRect();

        this.container.parentElement?.getBoundingClientRect();
        const parentRect = this.container.parentElement?.getBoundingClientRect();
        const size = Math.min(parentRect.width, parentRect.height) - 2;

        
        // Set actual canvas size for crisp rendering
        this.canvas.width = size;
        this.canvas.height = size;
        
        // Update radar parameters
        this.centerX = size / 2;
        this.centerY = size / 2;
        this.radius = (size / 2) - 20;
        
        this.initializeDrawingContext();
    }
    
    initializeDrawingContext() {
        this.ctx.strokeStyle = this.options.primaryColor;
        this.ctx.fillStyle = this.options.primaryColor;
        this.ctx.lineWidth = 1;
        this.ctx.font = `${Math.max(10, this.radius / 25)}px Courier New`;
    }
    
    // Public API methods
    setRange(range) {
        this.options.range = range;
    }
    
    setRPM(rpm) {
        this.options.rpm = rpm;
    }
    
    updateBeamPosition(angle) {
        this.beamAngle = angle % 360;
    }
    
    updateContacts(contactList) {
        const currentTime = Date.now();
        this.contacts.clear();
        
        contactList.forEach(contact => {
            this.contacts.set(contact.id, {
                ...contact,
                age: currentTime - contact.lastSeen
            });
        });
    }
    
    addContact(contact) {
        const currentTime = Date.now();
        this.contacts.set(contact.id, {
            ...contact,
            age: currentTime - contact.lastSeen
        });
    }
    
    removeContact(contactId) {
        this.contacts.delete(contactId);
    }
    
    clearContacts() {
        this.contacts.clear();
    }
    
    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
        }
        this.container.innerHTML = '';
    }
    
    // Drawing methods
    polarToCartesian(bearing, range) {
        const mathAngle = (90 - bearing) * Math.PI / 180;
        const normalizedRange = (range / this.options.range) * this.radius;
        
        const ret = {
            x: this.centerX + normalizedRange * Math.cos(mathAngle),
            y: this.centerY - normalizedRange * Math.sin(mathAngle)
        };
        return ret;
    }
    
    drawRangeRings() {
        this.ctx.strokeStyle = this.adjustColor(this.options.primaryColor, 0.3);
        this.ctx.lineWidth = 1;
        
        const rings = 4;
        const fontSize = Math.max(8, this.radius / 30);
        this.ctx.font = `${fontSize}px Courier New`;
        
        for (let i = 1; i <= rings; i++) {
            const ringRadius = (this.radius / rings) * i;
            this.ctx.beginPath();
            this.ctx.arc(this.centerX, this.centerY, ringRadius, 0, 2 * Math.PI);
            this.ctx.stroke();
            
            // Range labels
            const rangeValue = (this.options.range / rings) * i;
            this.ctx.fillStyle = this.adjustColor(this.options.primaryColor, 0.7);
            this.ctx.fillText(rangeValue.toString(), 
                this.centerX + ringRadius - fontSize * 1.5, 
                this.centerY - 5);
        }
    }
    
    drawBearingLines() {
        this.ctx.strokeStyle = this.adjustColor(this.options.primaryColor, 0.2);
        this.ctx.lineWidth = 1;
        
        const fontSize = Math.max(8, this.radius / 35);
        this.ctx.font = `${fontSize}px Courier New`;
        
        for (let bearing = 0; bearing < 360; bearing += 30) {
            const angle = (90 - bearing) * Math.PI / 180;
            const endX = this.centerX + this.radius * Math.cos(angle);
            const endY = this.centerY - this.radius * Math.sin(angle);
            
            this.ctx.beginPath();
            this.ctx.moveTo(this.centerX, this.centerY);
            this.ctx.lineTo(endX, endY);
            this.ctx.stroke();
            
            // Bearing labels
            const labelX = this.centerX + (this.radius + 15) * Math.cos(angle);
            const labelY = this.centerY - (this.radius + 15) * Math.sin(angle);
            this.ctx.fillStyle = this.adjustColor(this.options.primaryColor, 0.7);
            this.ctx.textAlign = 'center';
            this.ctx.fillText(bearing.toString().padStart(3, '0'), labelX, labelY + fontSize/2);
        }
        this.ctx.textAlign = 'left';
    }
    
    drawSweepBeam() {
        
        // Draw bright sweep line
        const sweepAngle = (90 - this.beamAngle) * Math.PI / 180;
        const endX = this.centerX + this.radius * Math.cos(sweepAngle);
        const endY = this.centerY - this.radius * Math.sin(sweepAngle);
        
        this.ctx.strokeStyle = this.options.primaryColor;
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.moveTo(this.centerX, this.centerY);
        this.ctx.lineTo(endX, endY);
        this.ctx.stroke();
    }
    
    drawContacts() {
        const currentTime = Date.now();
        const fontSize = Math.max(8, this.radius / 40);
        this.ctx.font = `${fontSize}px Courier New`;
        
        this.contacts.forEach(contact => {
            const age = currentTime - contact.lastSeen;
            const alpha = Math.max(0, 1 - (age / this.options.fadeTime || 10000));
            
            if (alpha > 0) {
                const pos = this.polarToCartesian(contact.bearing, contact.range);
                const contactSize = Math.max(2, this.radius / 100);
                
                // Draw contact dot
                this.ctx.fillStyle = this.adjustColor(this.options.contactColor, alpha);
                this.ctx.beginPath();
                this.ctx.arc(pos.x, pos.y, contactSize, 0, 2 * Math.PI);
                this.ctx.fill();
                
                // Draw contact ID
                this.ctx.fillStyle = this.adjustColor(this.options.contactColor, alpha * 0.8);
                this.ctx.fillText(contact.id.toString(), pos.x + contactSize + 2, pos.y - contactSize);
                
                // Draw range/bearing info for recent contacts
                if (age < 2000 && this.radius > 100) {
                    const info = `${Math.round(contact.range)}nm/${Math.round(contact.bearing)}°`;
                    this.ctx.fillText(info, pos.x + contactSize + 2, pos.y + fontSize + contactSize);
                }
            }
        });
    }
    
    adjustColor(color, alpha) {
        // Convert hex color to rgba
        if (color.startsWith('#')) {
            const r = parseInt(color.slice(1, 3), 16);
            const g = parseInt(color.slice(3, 5), 16);
            const b = parseInt(color.slice(5, 7), 16);
            return `rgba(${r}, ${g}, ${b}, ${alpha})`;
        }
        return color; // Return as-is if not hex
    }
    
    clear() {
        this.ctx.fillStyle = this.options.backgroundColor;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }
    
    draw() {
        this.clear();
        this.drawRangeRings();
        this.drawBearingLines();
        this.drawSweepBeam();
        this.drawContacts();
        this.updateInfoPanel();
        
        // Auto-rotate beam based on RPM
        const degreesPerSecond = (this.options.rpm * 360) / 60;
        const deltaTime = 16.67; // Assuming 60 FPS
        this.updateBeamPosition(this.beamAngle + (degreesPerSecond * deltaTime / 1000));
    }
    
    startAnimation() {
        const animate = () => {
            this.draw();
            this.animationId = requestAnimationFrame(animate);
        };
        animate();
    }
}

export { RadarDisplay };
