import { Engine, Scene, ArcRotateCamera, <PERSON>ector3, <PERSON><PERSON><PERSON><PERSON><PERSON>, Mesh<PERSON>uilder } from "babylonjs";
import { TowerDefenseGame } from "./game/game";
import { RadarDisplay } from "./radar_display";

declare global {
  interface Window {
    userCode: string;
    radarSystem: RadarDisplay;
  }
}

const canvas = document.getElementById("renderCanvas") as HTMLCanvasElement;

const game = new TowerDefenseGame(canvas);

if(!game) {
  throw new Error("Game not initialized");
}

console.log("Initializing game...");
game.init()

// Initialize radar system
// window.radarSystem = new RadarDisplay('radar-screen', {
//   range: game.radar.range,
//   rpm: game.radar.rpm,
//   showInfo: false
// });

window.addEventListener("resize", () => {
  game.engine.resize();
});

game.afterUpdate.push(() => {
  if(!game.radar) {
    return;
  }

  if(!window.radarSystem) {
    window.radarSystem = new RadarDisplay('radar-screen', {
      range: game.radar.range,
      rpm: game.radar.rpm,
      showInfo: false
    });
  }

  const contacts = game.radar.getAllContacts();
  // console.log(contacts);
  window.radarSystem.updateContacts(contacts);
  window.radarSystem.updateBeamPosition(game.radar.getBeamBearing());
}); 

const deployButton = document.getElementById("deploy_code") as HTMLButtonElement;

deployButton.addEventListener("click", () => {
  if(!game.turret) {
    console.error("Turret not initialized");
    return;
  }
  const userCode = window.userCode;


  game.turret.setFireControlFunction(userCode);
  
  // You can now pass userCode to your turret or execute it
  // For example: game.turret.executeUserCode(userCode);
});
