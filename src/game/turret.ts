import { Mesh, Scene, Vector3, <PERSON><PERSON><PERSON><PERSON>er, StandardMaterial, Color3, PhysicsImpostor } from 'babylonjs';
import { type Projectile } from './projectile';
import { type RadarContact } from './radar';

interface TurretUpdateResult {
    headingComplete: boolean;
    elevationComplete: boolean;
    allComplete: boolean;
}

interface TurretOptions {
    rotationSpeed?: number;
    elevationSpeed?: number;
    minElevation?: number;
    maxElevation?: number;
    muzzleVelocity?: number;
    projectileLifetime?: number;
    barrelLength?: number;
}

class ArtilleryTurret {
    private turretMesh: Mesh;
    private barrelMesh: Mesh;
    private scene: Scene;
    
    // Current heading and elevation
    private currentHeading: number = 0;  // 0-360 degrees (0 = North)
    private currentElevation: number = 0; // -90 to +90 degrees
    
    // Animation properties
    private rotationSpeed: number;
    /** Turret rotation speed in degrees per second */

    private elevationSpeed: number;
    /** Turret elevation speed in degrees per second */

    private minElevation: number;
    /** Minimum elevation angle in degrees */
    private maxElevation: number;
    /** Maximum elevation angle in degrees */
    
    // Firing properties
    private muzzleVelocity: number;
    private projectileLifetime: number;
    private barrelLength: number;
    
    // Store target values for smooth animation
    private targetHeading: number = 0;
    private targetElevation: number = 0;
    
    private isRotating: boolean = false;
    private isElevating: boolean = false;

    private fireControlFunction: string = "";

    private radarContacts: RadarContact[] = [];

    private userMemory: Map<string, any> = new Map();

    private directionIndicator: Mesh | null = null;

    // Public API

    public isLoaded: boolean = false;

    public position: Vector3;

    // Onfire event
    public onFire: (projectile: Projectile) => void = () => {};
    
    constructor(turretMesh: Mesh, barrelMesh: Mesh, scene: Scene, position: Vector3, options: TurretOptions = {}) {
        this.turretMesh = turretMesh;  // Base rotating part
        this.barrelMesh = barrelMesh;  // Elevation part
        this.scene = scene;
        this.position = position;
        
        // Set options with defaults
        this.rotationSpeed = options.rotationSpeed ?? 10.0; // degrees per second
        this.elevationSpeed = options.elevationSpeed ?? 8; // degrees per seconds
        this.minElevation = options.minElevation ?? -45;
        this.maxElevation = options.maxElevation ?? 85;
        this.muzzleVelocity = options.muzzleVelocity ?? 600; // m/s
        this.projectileLifetime = options.projectileLifetime ?? 60 * 20; // seconds
        this.barrelLength = options.barrelLength ?? 2; // meters from pivot to muzzle

        this.setElevation(0, false);
        this.rotateToHeading(0, false);
        this.isLoaded = true;
    }
    
    // Convert compass heading to Babylon.js Y rotation
    private headingToRotation(heading: number): number {
        // Compass: 0° = North, 90° = East, 180° = South, 270° = West
        // Babylon.js: 0° = positive Z axis, rotation is clockwise when viewed from above
        // Convert: heading 0° (North) should point to negative Z axis (-π/2)
        return (heading * Math.PI / 180) + Math.PI / 2;
    }
    
    // Convert elevation angle to Babylon.js X rotation
    private elevationToRotation(elevation: number): number {
        // Elevation: 0° = horizontal, +90° = straight up, -90° = straight down
        // Babylon.js X rotation: positive rotation tilts "up"
        return -elevation * Math.PI / 180;
    }
    
    // Get shortest rotation path (handles 0°/360° wrap-around)
    private getShortestRotationPath(current: number, target: number): number {
        let diff = target - current;
        
        // Normalize to -180 to +180 range
        while (diff > 180) diff -= 360;
        while (diff < -180) diff += 360;
        
        return diff;
    }
    
    // Main API method - rotate to specific heading
    private rotateToHeading(heading: number, smooth: boolean = true): void {
        // Clamp heading to 0-360 range
        heading = ((heading % 360) + 360) % 360;
        
        if (smooth) {
            this.targetHeading = heading;
            this.isRotating = true;
        } else {
            // Instant rotation
            this.currentHeading = heading;
            this.targetHeading = heading;
            this.turretMesh.rotation.y = this.headingToRotation(heading);
            this.isRotating = false;
        }
    }
    
    // Main API method - set elevation angle
    private setElevation(elevation: number, smooth: boolean = true): void {
        // Clamp elevation to configured range
        elevation = Math.max(this.minElevation, Math.min(this.maxElevation, elevation));
        
        if (smooth) {
            this.targetElevation = elevation;
            this.isElevating = true;
        } else {
            // Instant elevation
            this.currentElevation = elevation;
            this.targetElevation = elevation;
            this.barrelMesh.rotation.x = this.elevationToRotation(elevation) + Math.PI / 2;
            this.isElevating = false;
        }
    }
    
    // Combined aiming method
    public aimTo(heading: number, elevation: number): void {
        this.rotateToHeading(heading, true);
        this.setElevation(elevation, true);
    }
    
    // Call this in your render loop
    public update(): TurretUpdateResult {
        const deltaTime = this.scene.getEngine().getDeltaTime();

        const deltaSeconds = deltaTime / 1000;

        let headingComplete = false;
        let elevationComplete = false;
        
        // Handle heading rotation
        if (this.isRotating) {
            const headingDiff = this.getShortestRotationPath(this.currentHeading, this.targetHeading);
            
            if (Math.abs(headingDiff) < 0.1) {
                // Close enough, snap to target
                this.currentHeading = this.targetHeading;
                this.isRotating = false;
                headingComplete = true;
            } else {
                // Move towards target
                const step = Math.sign(headingDiff) * Math.min(this.rotationSpeed * deltaSeconds, Math.abs(headingDiff));
                this.currentHeading += step;
                
                // Handle wrap-around
                this.currentHeading = ((this.currentHeading % 360) + 360) % 360;
            }
            
            // Apply rotation to mesh
            this.turretMesh.rotation.y = this.headingToRotation(this.currentHeading);
        }
        
        // Handle elevation
        if (this.isElevating) {
            const elevationDiff = this.targetElevation - this.currentElevation;
            
            if (Math.abs(elevationDiff) < 0.1) {
                // Close enough, snap to target
                this.currentElevation = this.targetElevation;
                this.isElevating = false;
                elevationComplete = true;
            } else {
                // Move towards target
                const step = Math.sign(elevationDiff) * Math.min(this.elevationSpeed * deltaSeconds, Math.abs(elevationDiff));
                this.currentElevation += step;
            }
            
            // Apply elevation to mesh
            this.barrelMesh.rotation.x = (this.elevationToRotation(this.currentElevation)) + Math.PI / 2; // this will subtract 90 degrees so that 0 degrees is horizontal and 90 is straight up
        }

        // Update direction indicator
        this.updateDirectionIndicator();
        
        this.executeUserCode();
        
        return {
            headingComplete: !this.isRotating,
            elevationComplete: !this.isElevating,
            allComplete: !this.isRotating && !this.isElevating
        };
    }
    
    // Utility methods
    public getCurrentHeading(): number {
        return this.currentHeading;
    }
    
    public getCurrentElevation(): number {
        return this.currentElevation;
    }
    
    public isOnTarget(): boolean {
        return !this.isRotating && !this.isElevating;
    }
    
    // Set rotation speeds
    private setRotationSpeed(headingSpeed: number, elevationSpeed?: number): void {
        this.rotationSpeed = headingSpeed;
        if (elevationSpeed !== undefined) {
            this.elevationSpeed = elevationSpeed;
        }
    }
    
    // Get current target values
    public getTargetHeading(): number {
        return this.targetHeading;
    }
    
    public getTargetElevation(): number {
        return this.targetElevation;
    }
    
    // Check if turret is currently moving
    public isMoving(): boolean {
        return this.isRotating || this.isElevating;
    }
    
    // Stop all movement immediately
    public stop(): void {
        this.isRotating = false;
        this.isElevating = false;
        this.targetHeading = this.currentHeading;
        this.targetElevation = this.currentElevation;
    }
    
    // Get elevation limits
    public getElevationLimits(): { min: number; max: number } {
        return { min: this.minElevation, max: this.maxElevation };
    }
    
    // Calculate muzzle position in world space
    private getMuzzlePosition(): Vector3 {
        // Start at barrel pivot point (turret position)
        const turretPosition = this.turretMesh.getAbsolutePosition();
        
        // Use the same direction calculation as firing direction
        const direction = this.getFiringDirection();
        
        // Calculate muzzle position by moving along barrel direction
        const muzzlePosition = new Vector3(
            turretPosition.x + direction.x * this.barrelLength,
            turretPosition.y + direction.y * this.barrelLength,
            turretPosition.z + direction.z * this.barrelLength
        );

        return this.barrelMesh.getAbsolutePosition().add(this.barrelMesh.up.scale(this.barrelLength));
        
        // return muzzlePosition;
    }
    
    // Calculate firing direction vector
    private getFiringDirection(): Vector3 {
        const headingRad = this.headingToRotation(this.currentHeading);
        const elevationRad = this.elevationToRotation(this.currentElevation);
        
        // Calculate normalized direction vector
        const directionX = Math.sin(-headingRad) * Math.cos(-elevationRad);
        const directionY = Math.sin(-elevationRad);
        const directionZ = Math.cos(-headingRad) * Math.cos(-elevationRad);
        
        const direction = new Vector3(directionX, directionY, directionZ).normalize();

        const forward = this.barrelMesh.up;

        return forward;
    }
    
    // Fire the turret
    public fire(): Projectile | null {
        // Create projectile mesh
        const projectileMesh = MeshBuilder.CreateSphere("projectile", { 
            diameter: 0.2 
        }, this.scene);
        
        // Create projectile material
        const material = new StandardMaterial("projectileMaterial", this.scene);
        material.diffuseColor = Color3.Yellow();
        material.emissiveColor = Color3.FromHexString("#ff6600");
        projectileMesh.material = material;
        
        // Set projectile position to muzzle
        const muzzlePosition = this.getMuzzlePosition();
        projectileMesh.position = muzzlePosition;
        
        // Calculate velocity vector
        const direction = this.getFiringDirection();
        const velocity = direction.scale(this.muzzleVelocity);
        
        // Add physics impostor for realistic physics (optional)
        if (this.scene.getPhysicsEngine()) {
            projectileMesh.physicsImpostor = new PhysicsImpostor(
                projectileMesh, 
                PhysicsImpostor.SphereImpostor, 
                { mass: 1, restitution: 0.3 }, 
                this.scene
            );
            
            // Apply initial velocity
            projectileMesh.physicsImpostor.setLinearVelocity(velocity);
        }
        
        // Create projectile object
        const projectile: Projectile = {
            mesh: projectileMesh,
            velocity: velocity,
            createdTime: Date.now(),
            lifetime: this.projectileLifetime
        };
        
        // Add to active projectiles
        //this.activeProjectiles.push(projectile);
        
        console.log(`Fired projectile at heading ${this.currentHeading.toFixed(1)}°, elevation ${this.currentElevation.toFixed(1)}°`);
        console.log(`Muzzle velocity: ${this.muzzleVelocity} m/s`);

        this.onFire(projectile);
        
        return projectile;
    }
    
    // Set muzzle velocity
    public setMuzzleVelocity(velocity: number): void {
        this.muzzleVelocity = velocity;
    }
    
    // Get muzzle velocity
    public getMuzzleVelocity(): number {
        return this.muzzleVelocity;
    }

    public setFireControlFunction(userCode: string): void {
        this.fireControlFunction = userCode;
    }

    public setRadarContacts(contacts: RadarContact[]) {
        this.radarContacts = contacts;
    }

    public executeUserCode(): { success: boolean; error?: string; result?: any } {
        try {
            // Create a safe execution context with access to turret methods

            type TurretAPI = {
                aimTo: (heading: number, elevation: number, smooth?: boolean) => void;
                getCurrentHeading: () => number;
                getCurrentElevation: () => number;
                getTargetHeading: () => number;
                getTargetElevation: () => number;
                isOnTarget: () => boolean;
                isMoving: () => boolean;
                fire: () => Projectile | null;
                getMuzzleVelocity: () => number;
                stop: () => void;
                setRotationSpeed: (headingSpeed: number, elevationSpeed?: number) => void;
                getElevationLimits: () => { min: number; max: number };
                wait: (ms: number) => Promise<void>;
                log: (...args: any[]) => void;
                Math: typeof Math;
                radarContacts: RadarContact[];
                // Memory system
                setMemory: (key: string, value: any) => void;
                getMemory: (key: string) => any;
                hasMemory: (key: string) => boolean;
                clearMemory: (key?: string) => void;
            }
            const turretAPI = {
                // Movement methods
                aimTo: (heading: number, elevation: number, smooth: boolean = true) => this.aimTo(heading, elevation),
                
                // Status methods
                getCurrentHeading: () => this.getCurrentHeading(),
                getCurrentElevation: () => this.getCurrentElevation(),
                getTargetHeading: () => this.getTargetHeading(),
                getTargetElevation: () => this.getTargetElevation(),
                isOnTarget: () => this.isOnTarget(),
                isMoving: () => this.isMoving(),
                
                // Firing methods
                fire: () => this.fire(),
                getMuzzleVelocity: () => this.getMuzzleVelocity(),
                
                // Control methods
                stop: () => this.stop(),
                setRotationSpeed: (headingSpeed: number, elevationSpeed?: number) => this.setRotationSpeed(headingSpeed, elevationSpeed),
                getElevationLimits: () => this.getElevationLimits(),
                
                // Utility functions for user convenience
                wait: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
                log: (...args: any[]) => console.log('[User Code]:', ...args),
                
                // Math utilities
                Math: Math,

                radarContacts: this.radarContacts,
                // Memory system
                setMemory: (key: string, value: any) => this.userMemory.set(key, value),
                getMemory: (key: string) => this.userMemory.get(key),
                hasMemory: (key: string) => this.userMemory.has(key),
                clearMemory: (key?: string) => {
                    if (key! == undefined) {
                        this.userMemory.delete(key!);
                    } else {
                        this.userMemory.clear();
                    }
                }
            } as TurretAPI;
            
            // Create execution context
            const contextKeys = Object.keys(turretAPI);

            const contextVals = contextKeys.map(key => turretAPI[key as keyof TurretAPI]);
            
            // Create and execute the function with turret API in scope
            const userFunction = new Function(...contextKeys, this.fireControlFunction);
            const result = userFunction(...contextVals);
            
            // Handle both sync and async results
            if (result instanceof Promise) {
                result.then(
                    (asyncResult) => {
                        console.log('[User Code] Async execution completed:', asyncResult);
                    },
                    (error) => {
                        console.error('[User Code] Async execution error:', error);
                    }
                );
                return { success: true, result: 'Async execution started' };
            } else {
                return { success: true, result };
            }
            
        } catch (error) {
            console.error('[User Code] Execution error:', error);
            return { 
                success: false, 
                error: error instanceof Error ? error.message : String(error) 
            };
        }
    }

    // Update the visual direction indicator
    private updateDirectionIndicator(): void {
        // Remove existing indicator
        if (this.directionIndicator) {
            this.directionIndicator.dispose();
        }
        
        // Create new direction indicator
        this.directionIndicator = MeshBuilder.CreateCylinder("directionIndicator", {
            height: 10,
            diameter: 0.1
        }, this.scene);
        
        // Red material
        const material = new StandardMaterial("directionMaterial", this.scene);
        material.diffuseColor = Color3.Red();
        material.emissiveColor = Color3.Red().scale(0.3);
        this.directionIndicator.material = material;
        
        // Position at muzzle
        const muzzlePos = this.getMuzzlePosition();
        const direction = this.getFiringDirection();
        
        // Position cylinder center 5m along the direction vector
        this.directionIndicator.position = muzzlePos.add(direction.scale(5));
        
        // // Align cylinder with direction vector
        const up = Vector3.Up();
        const right = Vector3.Cross(up, direction).normalize();
        const forward = Vector3.Cross(right, up).normalize();
        
        // // Create rotation matrix and apply to cylinder
        this.directionIndicator.lookAt(muzzlePos.add(direction.scale(10)));
        this.directionIndicator.rotation.x = this.directionIndicator.rotation.x + Math.PI / 2;
    }
}

export { ArtilleryTurret, TurretUpdateResult, TurretOptions, Projectile , RadarContact};
