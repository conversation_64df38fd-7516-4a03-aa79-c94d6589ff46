import { Scene, Vector3 } from "babylonjs";
import { Enemy } from "./enemies/basic";

type RadarContact = {
    id: string;
    bearing: number;
    range: number;
    heading: number;
    speed: number;
    altitude: number;
    lastSeen: number;
    type: string;
}

type RadarOptions = {
    rpm?: number;
    range?: number;
}

class RadarSystem {

    private trackedContacts: RadarContact[] = [];

    private contacts: RadarContact[] = [];

    /** Rotations per minute */
    public rpm: number;
    
    /** Radar range in meters */
    public range: number;
    

    private scene: Scene;

    private beamBearing: number;

    constructor(scene: Scene, radarOptions: RadarOptions = {}) {
        this.scene = scene;
        this.rpm = radarOptions.rpm ?? 10;
        this.range = radarOptions.range ?? 10000;
        this.beamBearing = 0;
        this.trackedContacts = [];
        this.contacts = [];
    }

    public update(enemies: Enemy[]): void {
        const deltaTime = this.scene.getEngine().getDeltaTime();

        const deltaSeconds = deltaTime / 1000;

        // calculate how many degrees the radar has rotated since the last frame

        const degreesPerSecond = (this.rpm / 60) * 360;

        const degreesRotated = degreesPerSecond * deltaSeconds;

        // for each enemy, check if it is within the radar range
        const enemiesInRadarRange = enemies.filter(enemy => {
            return enemy.position.length() <= this.range;
        });

        // for each enemy in the radar range, check if it would have been hit by the radar bean over the last frame
        enemiesInRadarRange.forEach(enemy => {
            // calculate the bearing to the enemy
            const bearing = Math.atan2(enemy.position.x, enemy.position.z) * 180 / Math.PI;

            // check if the bearing is within the sweep since last frame
            if(this.beamBearing <= bearing && bearing <= this.beamBearing + degreesRotated) {
                // check if we've seen this contact before
                let alreadyTracked = false;
                for(const contact of this.contacts) {
                    if(contact.id == enemy.id) {
                        alreadyTracked = true;

                        // we have seen this contact before. We can use its last seen position and time to calculate the heading and speed
                        const timeSinceLastSeen = (Date.now() - contact.lastSeen) / 1000;
                        // we can figure out how far and in which direction it has moved by using trigonometry with its current bearing and distance and its last bearing and distance
                        const lastBearing = contact.bearing;
                        const lastDistance = contact.range;
                        const currentDistance = enemy.position.length();

                        // figure out the length of the opposite side of the triangle formed by the last and current position using the law of cosines
                        const deltaBearing = bearing - lastBearing;
                        const opposite = Math.sqrt(Math.pow(currentDistance, 2) + Math.pow(lastDistance, 2) - 2 * currentDistance * lastDistance * Math.cos(deltaBearing * Math.PI / 180));
                        const heading = Math.asin(opposite / currentDistance) * 180 / Math.PI;
                        const speed = opposite / timeSinceLastSeen;

                        contact.bearing = bearing;
                        contact.heading = heading;
                        contact.range = currentDistance;
                        contact.speed = speed;
                        contact.altitude = enemy.position.y;
                        contact.lastSeen = Date.now();

                        const contactIndex = this.trackedContacts.findIndex(c => c.id == enemy.id);
                        if (contactIndex == -1) {
                            this.trackedContacts.push(contact);
                        } else {
                            this.trackedContacts[contactIndex] = contact;
                        }
                    }
                }
                
                // New contact. We haven't seen this before
                if(!alreadyTracked) {
                    console.log("New contact");
                    console.log("Range: ", enemy.position.length());
                    console.log("Bearing: ", bearing);
                    this.contacts.push({
                        id: enemy.id,
                        bearing: bearing,
                        range: enemy.position.length(),
                        heading: -1, // We have only seen it once so we don't know the heading
                        speed: -1, // We have only seen it once so we don't know the speed
                        altitude: enemy.position.y,    
                        lastSeen: Date.now(),
                        type: "hostile"
                    });
                }
            }
        });
        const newBeamBearing = (this.beamBearing + degreesRotated) % 360;
        this.beamBearing = newBeamBearing;
    }

    public getTrackedContacts(): RadarContact[] {
        return this.trackedContacts;
    }

    public getAllContacts(): RadarContact[] {
        return this.contacts;
    }

    public getBeamBearing(): number {
        return this.beamBearing;
    }
}

export { RadarSystem, RadarContact, RadarOptions };
