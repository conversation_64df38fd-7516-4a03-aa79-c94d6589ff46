import { Vector3 } from "babylonjs";

type GlobalPhysicsSettings = {
    airDensity: number; // kg/m^3, typically ~1.225 at sea level
    airResistanceEnabled: boolean;
    gravity: Vector3;
}

type PhysicsSettings = {
    gravity: Vector3; // e.g., new Vector3(0, -9.81, 0)
    airResistanceEnabled: boolean;
    airDensity: number; // kg/m^3, typically ~1.225 at sea level
}

const defaultPhysicsSettings: GlobalPhysicsSettings = {
    gravity: new Vector3(0, -9.81, 0),
    airResistanceEnabled: false,
    airDensity: 1.225,
}

export { GlobalPhysicsSettings, defaultPhysicsSettings };