import { Vector3 } from "babylonjs";
import { GlobalPhysicsSettings } from "../settings";
import { AProjectile } from "./core";

class BallisticProjectile extends AProjectile {
    protected updatePhysics(deltaTime: number, globalSettings: GlobalPhysicsSettings) {
        let acceleration = globalSettings.gravity.clone();

        if (globalSettings.airResistanceEnabled) {
            const dragForce = this.computeDragForce(globalSettings.airDensity);
            const dragAccel = dragForce.scale(1 / this.settings.projectileMass);
            acceleration.addInPlace(dragAccel);
        }
        
        this.velocity.addInPlace(acceleration.scale(deltaTime));
        this.position.addInPlace(this.velocity.scale(deltaTime));
    }

    private computeDragForce(airDensity: number): Vector3 {
        const speed = this.velocity.length();
        if (speed === 0) return Vector3.Zero();

        const dragMagnitude =
            0.5 *
            airDensity *
            speed ** 2 *
            this.settings.dragCoefficient *
            this.settings.projectileArea;

        // Drag acts opposite to velocity
        const dragDirection = this.velocity.normalize().scale(-1);
        return dragDirection.scale(dragMagnitude);
    }

    isBelowGround(): boolean {
        return this.position.y <= 0;
    }
}

export { BallisticProjectile };