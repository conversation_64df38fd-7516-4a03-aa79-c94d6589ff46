import { Mesh, Vector3 } from "babylonjs";
import { GlobalPhysicsSettings } from "../settings";

type ProjectilePhysicsSettings = {
    dragCoefficient: number;
    projectileArea: number;
    projectileMass: number;
}

abstract class AProjectile {

    protected mesh: Mesh;

    public position: Vector3;
    public velocity: Vector3;
    protected settings: ProjectilePhysicsSettings;

    public readonly createdTime: number;
    public readonly originPosition: Vector3;

    public damageRadius: number;
    public damageAmount: number;

    constructor(
        mesh: Mesh,
        initialPosition: Vector3,
        initialVelocity: Vector3,
        physicsSettings: ProjectilePhysicsSettings,
        damageRadius: number,
        damageAmount: number,
    ) {
        this.mesh = mesh;
        this.position = initialPosition.clone();
        this.velocity = initialVelocity.clone();
        this.settings = physicsSettings;
        this.damageRadius = damageRadius;
        this.damageAmount = damageAmount;
        this.createdTime = Date.now();
        this.originPosition = initialPosition.clone();
    }

    protected abstract updatePhysics(deltaTime: number, globalSettings: GlobalPhysicsSettings): void;

    public update(deltaTime: number, globalSettings: GlobalPhysicsSettings): void {
        if(this.mesh.isDisposed() || this.isBelowGround()) {
            return;
        }
        this.updatePhysics(deltaTime, globalSettings);

        this.mesh.position = this.position;

        // Check if below ground
        if(this.isBelowGround()) {
            this.mesh.dispose();

            const distanceTraveled = this.position.subtract(this.originPosition).length();

            console.log('Projectile hit the ground at position: ', this.position);
            console.log("Flight time: ", (Date.now() - this.createdTime) / 1000, "seconds");
            console.log("Distance traveled: ", distanceTraveled, " meters");
        }
    }

    public isBelowGround(): boolean {
        return this.position.y <= 0;
    }

    public getProjectileMesh(): Mesh {
        return this.mesh;
    }
}

export { AProjectile, ProjectilePhysicsSettings };