import { Enemy } from "../enemies/basic";
import { DummyEne<PERSON> } from "../enemies/dummy";

type Wave = {
    // list of enemy types
    enemies: { new (...args: any[]): Enemy }[];
    // time between spawns in milliseconds
    enemySpawnInterval: number;
}

type Level = {
    name: string;
    description: string;
    instructions: string;
    waves: Wave[];
}

const level1: Level = {
    name: "Level 1",
    description: "Basic introduction to the game",
    instructions: "Your first level! Welcome to Code Defense! Your first objective will be to hit a static target.",
    waves: [
        {
            enemies: [DummyEnemy],
            enemySpawnInterval: 1000
        }
    ]
}

export { Level, Wave };