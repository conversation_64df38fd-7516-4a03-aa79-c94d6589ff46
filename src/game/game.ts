import { 
    Engine, 
    Scene, 
    ArcRotateCamera,
    Vector3, 
    HemisphericLight, 
    MeshBuilder,
    Mesh,
    ShadowGenerator,
    Color3,
    Color4,
    StandardMaterial,
    DirectionalLight,
    GroundMesh,
    HavokPlugin

} from "babylonjs";
import { ArtilleryTurret } from "./turret";
import { Projectile } from "./projectile";
import { Enemy } from "./enemies/basic";
import { DummyEnemy } from "./enemies/dummy";
import { RadarSystem } from "./radar";
import { defaultPhysicsSettings, GlobalPhysicsSettings } from "./physics/settings";
import { AProjectile } from "./physics/projectiles";

export class TowerDefenseGame {
    private canvas: HTMLCanvasElement;
    public engine: Engine;
    public scene: Scene | undefined;
    private camera: ArcRotateCamera | undefined;
    private ground: GroundMesh | undefined;
    public turret: ArtilleryTurret | undefined;
    private base: Mesh | null;
    public enemies: Enemy[];
    private projectiles: AProjectile[];
    private shadowGenerator: ShadowGenerator | undefined;
    public radar: RadarSystem | undefined;

    public physicsSettings: GlobalPhysicsSettings = defaultPhysicsSettings;

    public afterUpdate: (() => void)[] = [];

    constructor(canvas: HTMLCanvasElement) {
        this.canvas = canvas;
        this.engine = new Engine(canvas, true);
        this.scene = undefined;
        this.camera = undefined;
        this.turret = undefined;
        this.base = null;
        this.enemies = [];
        this.projectiles = [];
    }

    async init() {
        await this.createScene();
        this.setupLighting()
        this.createTerrain();
        this.createTurret();
        this.setupCamera();
        this.createEnemies();

        if(!this.scene) {
            throw new Error("Scene failed to initialize");
        }

        this.radar = new RadarSystem(this.scene);

        if(!this.scene) {
            throw new Error("Scene not initialized");
        }

        this.scene.registerBeforeRender(() => {
            this.update();
        });

        this.engine.runRenderLoop(() => {
            if(this.scene && this.scene.activeCamera) {
                this.scene.render();
            }
        });        
    }

    private update() {
        if(!this.scene) {
            throw new Error("Scene not initialized");
        }
        
        if(!this.radar){
            this.radar = new RadarSystem(this.scene);
        }

        this.radar?.update(this.enemies);

        // console.log(this.radar?.getAllContacts());


        if(this.turret) {
            this.turret.setRadarContacts(this.radar.getTrackedContacts());
            const result = this.turret.update();
        }

        this.updateProjectiles();

        if(this.enemies) {
            this.enemies.forEach(enemy => {
                enemy.update();
            });   
        }

        if(this.afterUpdate){
            this.afterUpdate.forEach(callback => {
                callback();
            });
        }
    }

    async createScene() {
        this.scene = new Scene(this.engine);
        this.scene.clearColor = new Color4(0.1, 0.2, 0.3); // Dark blue background
        
        // Enable physics
        this.scene.enablePhysics();
        
        return this.scene;
    }

    createTerrain() {
        // Create ground plane
        const ground = MeshBuilder.CreateGround("ground", {
            width: 160934,
            height: 160934,
            subdivisions: 35
        }, this.scene);

        // Ground material
        const groundMaterial = new StandardMaterial("groundMaterial", this.scene);
        groundMaterial.diffuseColor = new Color3(0.3, 0.4, 0.2); // Dark green
        groundMaterial.specularColor = new Color3(0.1, 0.1, 0.1);
        ground.material = groundMaterial;
        ground.receiveShadows = true;

        // Add some terrain features (hills/obstacles)
        this.createTerrainFeatures();
        
        this.ground = ground;
    }

    createTerrainFeatures() {
        // Create some hills as obstacles
        const hill1 = MeshBuilder.CreateSphere("hill1", {diameter: 20}, this.scene);
        hill1.position = new Vector3(-40, -.5, 30);
        hill1.scaling.y = 0.3; // Flatten to make it hill-like
        
        const hill2 = MeshBuilder.CreateSphere("hill2", {diameter: 15}, this.scene);
        hill2.position = new Vector3(50, -.5, -20);
        hill2.scaling.y = 0.4;

        // Hill material
        const hillMaterial = new StandardMaterial("hillMaterial", this.scene);
        hillMaterial.diffuseColor = new Color3(0.4, 0.3, 0.2); // Brown
        hillMaterial.specularPower = 0.1;
        hillMaterial.specularColor = new Color3(0.1, 0.1, 0.1);
        hill1.material = hillMaterial;
        hill2.material = hillMaterial;

        if (!this.shadowGenerator) {
            throw new Error("Shadow generator not initialized");
        }

        // Add shadows
        this.shadowGenerator.addShadowCaster(hill1);
        this.shadowGenerator.addShadowCaster(hill2);
    }

    createTurret() {
        // Turret base
        const turretBase = MeshBuilder.CreateCylinder("turretBase", {
            height: 2,
            diameter: 3
        }, this.scene);
        turretBase.position = new Vector3(0, 0, 0);

        // Turret barrel
        const turretBarrel = MeshBuilder.CreateCylinder("turretBarrel", {
            height: 4,
            diameterTop: 0.3,
            diameterBottom: 0.5
        }, this.scene);
        turretBarrel.position = new Vector3(0, 3, 0);
        turretBarrel.setPivotPoint(new Vector3(0, -2, 0)); // Set pivot point to bottom
        turretBarrel.rotation.x = Math.PI / 2;

        // Turret material
        const turretBarrelMaterial = new StandardMaterial("turretBaseMaterial", this.scene);
        turretBarrelMaterial.diffuseColor = new Color3(0.4, 0.4, 0.4); // Gray
        turretBarrelMaterial.specularPower = 0.9;
        
        // Make the turret base have a darker gray color
        const turretBaseMaterial = new StandardMaterial("turretBarrelMaterial", this.scene);
        turretBaseMaterial.diffuseColor = new Color3(0.27, 0.27, 0.27); // Dark gray

        turretBase.material = turretBaseMaterial;
        turretBarrel.material = turretBarrelMaterial;

        // Parent barrel to base for rotation
        turretBarrel.parent = turretBase;

        if (!this.shadowGenerator) {
            throw new Error("Shadow generator not initialized");
        }

        this.shadowGenerator.addShadowCaster(turretBase);
        this.shadowGenerator.addShadowCaster(turretBarrel);

        if(!this.scene) {
            throw new Error("Scene not initialized");
        }
        this.turret = new ArtilleryTurret(turretBase, turretBarrel, this.scene, new Vector3(0, 0, 0));

        this.turret.onFire = (projectile) => {
            console.log("Projectile fired");
            this.projectiles.push(projectile);
            this.shadowGenerator?.addShadowCaster(projectile.getProjectileMesh());
        }
    }

    setupCamera() {
        // Arc rotate camera for good tactical view
        this.camera = new ArcRotateCamera("camera", 
            Math.PI / 4,    // Alpha (horizontal rotation)
            Math.PI / 3,    // Beta (vertical rotation) 
            60,             // Radius
            new Vector3(0, 0, -40), // Target (slightly behind turret)
            this.scene
        );

        // Camera controls
        this.camera.attachControl(this.canvas, true);
        this.camera.setTarget(new Vector3(0, 0, 0));
        
        // Limit camera movement
        this.camera.lowerBetaLimit = 0.1;
        this.camera.upperBetaLimit = Math.PI / 2.2;
        this.camera.lowerRadiusLimit = 20;
        this.camera.upperRadiusLimit = 100;
    }

    setupLighting() {
        // Ambient light
        const ambientLight = new HemisphericLight("ambientLight", new Vector3(0, 1, 0), this.scene);
        ambientLight.intensity = 0.4;
        ambientLight.diffuse = new Color3(0.8, 0.8, 1.0);

        // Directional light (sun)
        const directionalLight = new DirectionalLight("sunLight", new Vector3(-1, -1, -0.5), this.scene);
        directionalLight.intensity = 0.8;
        directionalLight.diffuse = new Color3(1.0, 0.9, 0.7);
        
        // Shadow generator
        const shadowGenerator = new ShadowGenerator(2048, directionalLight);
        shadowGenerator.useExponentialShadowMap = true;
        this.shadowGenerator = shadowGenerator;
    }

    createEnemies() {
        this.enemies.push(new DummyEnemy(
                new Vector3(2000, 0, 1800),
                new Vector3(0, 0, 0), 
                100
            )
        )
    }

    damageEnemies(impactPosition: Vector3, damageRadius: number, damageAmount: number): void {
        this.enemies.forEach(enemy => {
            const distance = enemy.position.subtract(impactPosition).length();
            if (distance <= damageRadius) {
                // Calculate damage based impact position and damage radius
                const damage = damageAmount * (1 - distance / damageRadius);
                enemy.health -= damage;
                console.log(`Enemy ${enemy.id} hit. Health: ${enemy.health}`);
                if (enemy.health <= 0) {
                    enemy.mesh.dispose();
                }
            }
        });

        this.enemies = this.enemies.filter(enemy => enemy.health > 0);
    }

    updateProjectiles() {
        if(!this.scene) {
            throw new Error("Scene not initialized");
        }

        const currentTime = Date.now();
        const deltaTime = this.scene.getEngine().getDeltaTime() / 1000; // Convert to seconds
        
        // Update projectiles (if not using physics engine)
        // if (!this.scene.getPhysicsEngine()) {
        //     for (const projectile of this.projectiles) {
        //         // Apply gravity
        //         projectile.velocity.y -= 9.81 * deltaTime;
                
        //         // Update position
        //         projectile.mesh.position.addInPlace(projectile.velocity.scale(deltaTime));
        //     }
        // }

        for (const projectile of this.projectiles) {
            projectile.update(deltaTime, this.physicsSettings);
        }

        // Remove projectiles that have hit the ground
        this.projectiles = this.projectiles.filter(projectile => {
            if (projectile.isBelowGround()) {
                const originPosition = new Vector3()

                // Kill enemies in the area
                this.damageEnemies(projectile.position, projectile.damageRadius, projectile.damageAmount);

                return false;
            }
            
            return true;
        });
    }

}
