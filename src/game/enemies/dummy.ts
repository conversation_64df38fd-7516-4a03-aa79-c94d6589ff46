import { Mesh<PERSON><PERSON><PERSON>, Vector3 } from "babylonjs";
import { Enemy } from "./basic";

class DummyEnemy extends Enemy {
    constructor(position: Vector3, velocity: Vector3, health: number) {
        const mesh = MeshBuilder.CreateBox("dummy", { size: 1 });
        super(mesh, position, velocity, health);
    }
    public update(): void {
        // Do nothing
    }
}

export { DummyEnemy };