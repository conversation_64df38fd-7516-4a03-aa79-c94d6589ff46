import { Mesh, <PERSON><PERSON><PERSON><PERSON><PERSON>, Scene, ShadowGenerator, Vector3 } from "babylonjs";

export abstract class Enemy {
    public id: string;
    public position: Vector3;
    public velocity: Vector3;
    public health: number;
    public mesh: Mesh;

    constructor(mesh: Mesh, position: Vector3, velocity: Vector3, health: number) {
        this.id = Enemy.generateId();
        this.position = position;
        this.velocity = velocity;
        this.health = health;
        this.mesh = mesh;
    }

    private static nextId = 0;

    private static generateId() {
        return `enemy_${Enemy.nextId++}`;
    }
    
    public abstract update(): void;

}

export class DummyEnemy extends Enemy {
    public update(): void {
        // Do nothing
    }
}



// class BasicEnemy extends Enemy {
//     private mesh: Mesh;
//     private scene: Scene;
//     private target: Vector3;
//     private speed: number;
//     private health: number;
//     private maxHealth: number;
//     private isDead: boolean;
//     private shadowGenerator: ShadowGenerator | undefined;
    
//     constructor(scene: Scene) {
//         this.mesh = this.buildMesh(scene);
//         this.scene = scene;
//     }

//     private buildMesh(scene: Scene) {
//         // Build a super simple little tank mesh
        
//         // tank body
//         const mesh = MeshBuilder.CreateBox("enemy", { size: 1 }, scene);

//         // tank turret
//         const turret = MeshBuilder.CreateCylinder("turret", { height: 0.5, diameterTop: 0.5, diameterBottom: 0.5 }, scene);
//         turret.position = new Vector3(0, 0.25, 0);
//         turret.parent = mesh;

//         // add gun to turret
//         const gun = MeshBuilder.CreateCylinder("gun", { height: 1, diameterTop: 0.2, diameterBottom: 0.2 }, scene);
//         gun.position = new Vector3(0, 0.5, 0);
//         gun.parent = turret;

//         return mesh;
        
//     }

//     public update() {
//         // Move towards target
//         const direction = this.target.subtract(this.mesh.position).normalize();
//         this.mesh.position.addInPlace(direction.scale(this.speed * this.scene.getEngine().getDeltaTime() / 1000));
//     }

// }