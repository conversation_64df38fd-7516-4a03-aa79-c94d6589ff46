import { Mesh, <PERSON><PERSON><PERSON><PERSON><PERSON>, Scene, ShadowGenerator, Vector3 } from "babylonjs";

type EnemySettings = {
    health: number;
    speed: number;
    size: number;
}

/**
 * Basic enemy class.
 */
abstract class Enemy {
    /** Unique ID */
    public id: string;
    /** Position in world coordinates */
    public position: Vector3;
    /** Velocity in world coordinates */
    public velocity: Vector3;
    /** Health points */
    public health: number;
    /** Mesh */
    public mesh: Mesh;

    /**
     * @param mesh Mesh to use for enemy
     * @param position Initial position
     * @param velocity Initial velocity
     * @param health Initial health
     */
    constructor(mesh: Mesh, position: Vector3, velocity: Vector3, health: number) {
        this.id = Enemy.generateId();
        this.position = position;
        this.velocity = velocity;
        this.health = health;
        this.mesh = mesh;
    }

    private static nextId = 0;

    private static generateId() {
        return `enemy_${Enemy.nextId++}`;
    }
    
    public abstract update(): void;

}

const buildTankMesh = (scene: Scene) => {
    // Build a super simple little tank mesh
        
    // tank body
    var body = MeshBuilder.CreateBox("tankBody", {
        width: 1,
        height: 0.6,
        depth: 1.8
    });

    var turret = MeshBuilder.CreateCylinder("tankTurret", {
        height: .4,
        diameter: 0.8
    })
    turret.position.y = 0.6;
    turret.position.z = -0.2;
    turret.setParent(body);

    var leftTrack = MeshBuilder.CreateBox("tankLeftTrack",
        {
            width: 0.2,
            height:0.3,
            depth: 2
        }
    )
    leftTrack.position.x = .6;
    leftTrack.position.y = -.2;
    leftTrack.setParent(body);

    var rightTrack = MeshBuilder.CreateBox("tankRightTrack",
        {
            width: 0.2,
            height:0.3,
            depth: 2
        }
    )
    rightTrack.position.x = -0.6;
    rightTrack.position.y = -.2;
    rightTrack.setParent(body);

    var gun = MeshBuilder.CreateCylinder("tankGun", {
        height: 1.6,
        diameterTop: 0.1 ,
        diameterBottom: 0.18
    });
    gun.position.y = 0.6;
    gun.rotation.x = -Math.PI / 2;
    gun.position.z = -1;
    gun.setParent(turret);

    body.rotation.y = (Math.PI / 2) / 2;

    return body;
    
}

class BasicEnemy extends Enemy {
    private scene: Scene;
    private target: Vector3;
    private speed: number;
    private isDead: boolean;
    private shadowGenerator: ShadowGenerator | undefined;
    
    constructor(settings: EnemySettings, position: Vector3, velocity: Vector3, scene: Scene) {
        super(buildTankMesh(scene), position, velocity, settings.health);
        this.scene = scene;
        this.speed = settings.speed;
        this.isDead = false;
        this.target = new Vector3(0, 0, 0);
    }

    public setTarget(target: Vector3) {
        this.target = target;
    }

    public update() {
        // Move towards target
        const direction = this.target.subtract(this.mesh.position).normalize();
        this.mesh.position.addInPlace(direction.scale(this.speed * this.scene.getEngine().getDeltaTime() / 1000));
    }

}

export { Enemy, DummyEnemy, BasicEnemy };
