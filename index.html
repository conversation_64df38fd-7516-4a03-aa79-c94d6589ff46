<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Tower Defense: Code Combat</title>
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;700&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <style>
      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }

      html, body {
        width: 100%;
        height: 100%;
        font-family: 'JetBrains Mono', monospace;
        background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
        color: #e8e8e8;
        overflow-x: hidden;
      }

      .landing-container {
        min-height: 100vh;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 2rem;
        position: relative;
      }

      .background-grid {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image:
          linear-gradient(rgba(0, 255, 0, 0.1) 1px, transparent 1px),
          linear-gradient(90deg, rgba(0, 255, 0, 0.1) 1px, transparent 1px);
        background-size: 50px 50px;
        z-index: -1;
        animation: gridPulse 4s ease-in-out infinite;
      }

      @keyframes gridPulse {
        0%, 100% { opacity: 0.3; }
        50% { opacity: 0.6; }
      }

      .title-section {
        text-align: center;
        margin-bottom: 3rem;
        z-index: 1;
      }

      .main-title {
        font-family: 'Orbitron', monospace;
        font-size: 4rem;
        font-weight: 900;
        color: #00ff00;
        text-shadow: 0 0 20px rgba(0, 255, 0, 0.5);
        margin-bottom: 0.5rem;
        animation: titleGlow 2s ease-in-out infinite alternate;
      }

      @keyframes titleGlow {
        from { text-shadow: 0 0 20px rgba(0, 255, 0, 0.5); }
        to { text-shadow: 0 0 30px rgba(0, 255, 0, 0.8), 0 0 40px rgba(0, 255, 0, 0.3); }
      }

      .subtitle {
        font-size: 1.5rem;
        color: #888;
        font-weight: 400;
        margin-bottom: 1rem;
      }

      .tagline {
        font-size: 1.1rem;
        color: #aaa;
        font-style: italic;
      }

      .game-info {
        max-width: 800px;
        margin-bottom: 3rem;
        z-index: 1;
      }

      .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
      }

      .info-card {
        background: rgba(0, 0, 0, 0.6);
        border: 1px solid rgba(0, 255, 0, 0.3);
        border-radius: 8px;
        padding: 1.5rem;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
      }

      .info-card:hover {
        border-color: rgba(0, 255, 0, 0.6);
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 255, 0, 0.1);
      }

      .info-card h3 {
        color: #00ff00;
        font-family: 'Orbitron', monospace;
        font-size: 1.2rem;
        margin-bottom: 0.5rem;
      }

      .info-card p {
        color: #ccc;
        line-height: 1.6;
        font-size: 0.9rem;
      }

      .features-list {
        background: rgba(0, 0, 0, 0.6);
        border: 1px solid rgba(0, 255, 0, 0.3);
        border-radius: 8px;
        padding: 2rem;
        backdrop-filter: blur(10px);
      }

      .features-list h3 {
        color: #00ff00;
        font-family: 'Orbitron', monospace;
        font-size: 1.3rem;
        margin-bottom: 1rem;
        text-align: center;
      }

      .features-list ul {
        list-style: none;
        columns: 2;
        column-gap: 2rem;
      }

      .features-list li {
        color: #ccc;
        margin-bottom: 0.5rem;
        padding-left: 1rem;
        position: relative;
      }

      .features-list li::before {
        content: "▶";
        color: #00ff00;
        position: absolute;
        left: 0;
      }

      .action-section {
        text-align: center;
        z-index: 1;
      }

      .start-button {
        font-family: 'Orbitron', monospace;
        font-size: 1.3rem;
        font-weight: 700;
        color: #000;
        background: linear-gradient(45deg, #00ff00, #00cc00);
        border: none;
        padding: 1rem 3rem;
        border-radius: 8px;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 1px;
        box-shadow: 0 4px 15px rgba(0, 255, 0, 0.3);
      }

      .start-button:hover {
        background: linear-gradient(45deg, #00cc00, #00aa00);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 255, 0, 0.4);
      }

      .start-button:active {
        transform: translateY(0);
      }

      .coming-soon {
        margin-top: 1rem;
        color: #666;
        font-size: 0.9rem;
        font-style: italic;
      }

      @media (max-width: 768px) {
        .main-title {
          font-size: 2.5rem;
        }

        .subtitle {
          font-size: 1.2rem;
        }

        .features-list ul {
          columns: 1;
        }

        .landing-container {
          padding: 1rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="background-grid"></div>

    <div class="landing-container">
      <div class="title-section">
        <h1 class="main-title">CODE COMBAT</h1>
        <h2 class="subtitle">Tower Defense</h2>
        <p class="tagline">Program your way to victory</p>
      </div>

      <div class="game-info">
        <div class="info-grid">
          <div class="info-card">
            <h3>🎯 Strategic Programming</h3>
            <p>Write JavaScript code to control advanced artillery turrets. Program targeting algorithms, fire control systems, and tactical responses to incoming threats.</p>
          </div>

          <div class="info-card">
            <h3>🛡️ Defensive Warfare</h3>
            <p>Protect your base from waves of enemies using programmable defense systems. Each level introduces new challenges requiring smarter code solutions.</p>
          </div>

          <div class="info-card">
            <h3>📡 Radar Systems</h3>
            <p>Utilize realistic radar tracking and target acquisition systems. Process contact data and implement sophisticated tracking algorithms.</p>
          </div>

          <div class="info-card">
            <h3>⚡ Real-time Combat</h3>
            <p>Experience physics-based ballistics and real-time 3D combat. Your code executes in a realistic military simulation environment.</p>
          </div>
        </div>

        <div class="features-list">
          <h3>Game Features</h3>
          <ul>
            <li>Advanced 3D graphics with Babylon.js</li>
            <li>Realistic ballistics and physics</li>
            <li>Monaco code editor integration</li>
            <li>Progressive difficulty levels</li>
            <li>Radar and sensor simulation</li>
            <li>Multiple weapon systems</li>
            <li>Dynamic enemy AI</li>
            <li>Performance optimization challenges</li>
          </ul>
        </div>
      </div>

      <div class="action-section">
        <a href="game.html" class="start-button">Start Mission</a>
        <p class="coming-soon">Level progression system coming soon</p>
      </div>
    </div>
  </body>
</html>