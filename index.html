<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Tower Defense: Code Combat</title>
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;700&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <style>
      * {
        box-sizing: border-box;
      }
      
      html, body {
        margin: 0;
        padding: 0;
        overflow: hidden;
        width: 100%;
        height: 100%;
        font-family: 'JetBrains Mono', monospace;
        background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
        color: #e8e8e8;
      }
      
      .screen_container {
        display: flex;
        flex-direction: row;
        width: 100%;
        height: 100%;
        gap: 2px;
        background: #0a0a0a;
        padding: 2px;
      }
      
      .game-section {
        width: 50%;
        height: 100%;
        display: flex;
        flex-direction: column;
        gap: 2px;
      }
      
      canvas {
        width: 100%;
        height: 60%;
        display: block;
        border-radius: 8px;
        box-shadow: 
          0 0 20px rgba(0, 255, 255, 0.1),
          inset 0 0 20px rgba(0, 0, 0, 0.5);
        border: 2px solid #1e3a5f;
        background: radial-gradient(circle at center, #001122 0%, #000811 100%);
      }
      
      .radar-container {
        width: 100%;
        height: 40%;
        background: linear-gradient(180deg, #0e1419 0%, #1a1f2e 100%);
        border-radius: 8px;
        border: 2px solid #2d3748;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        position: relative;
      }
      
      .radar-header {
        background: linear-gradient(90deg, #1a202c 0%, #2d3748 100%);
        padding: 8px 16px;
        border-bottom: 2px solid #4a5568;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
      }
      
      .radar-title {
        font-family: 'Orbitron', monospace;
        font-weight: 700;
        font-size: 14px;
        color: #00ff88;
        text-transform: uppercase;
        letter-spacing: 1px;
        text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
      }
      
      .radar-info {
        font-size: 11px;
        color: #a0aec0;
        display: flex;
        gap: 12px;
      }
      
      .radar-display {
        flex: 1;
        position: relative;
        background: radial-gradient(circle at center, #001a00 0%, #000d00 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
      }
      
      .radar-screen {
        /* width: 90%;
        height: 90%;
        position: relative;
        border: 2px solid #00ff88;
        border-radius: 50%;
        box-shadow: 
          0 0 30px rgba(0, 255, 136, 0.3),
          inset 0 0 30px rgba(0, 255, 136, 0.1); */
      }
      
      .radar-screen::before {
        /* content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, #00ff88, transparent);
        transform: translateY(-50%); */
      }
      
      .radar-screen::after {
        /* content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: 50%;
        width: 1px;
        background: linear-gradient(0deg, transparent, #00ff88, transparent);
        transform: translateX(-50%); */
      }
      
      .radar-sweep {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 2px;
        height: 45%;
        background: linear-gradient(to top, #00ff88, transparent);
        transform-origin: bottom center;
        transform: translate(-50%, -100%) rotate(0deg);
        box-shadow: 0 0 10px #00ff88;
      }
      
      .radar-contact {
        position: absolute;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: opacity 0.3s ease, transform 0.3s ease;
      }
      
      .radar-contact.fresh {
        animation: contactPulse 1.5s ease-in-out infinite;
      }
      
      .radar-contact.fading {
        animation: contactFade 2s ease-in-out infinite;
      }
      
      .radar-contact.stale {
        animation: contactBlink 3s ease-in-out infinite;
      }
      
      @keyframes contactPulse {
        0%, 100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        50% { opacity: 0.8; transform: translate(-50%, -50%) scale(1.2); }
      }
      
      @keyframes contactFade {
        0%, 100% { opacity: 0.6; transform: translate(-50%, -50%) scale(0.9); }
        50% { opacity: 0.4; transform: translate(-50%, -50%) scale(1); }
      }
      
      @keyframes contactBlink {
        0%, 100% { opacity: 0.3; transform: translate(-50%, -50%) scale(0.8); }
        50% { opacity: 0.1; transform: translate(-50%, -50%) scale(0.9); }
      }
      
      .range-rings {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        pointer-events: none;
      }
      
      .range-ring {
        position: absolute;
        border: 1px solid rgba(0, 255, 136, 0.2);
        border-radius: 50%;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
      
      .sidebar {
        width: 50%;
        height: 100%;
        display: flex;
        flex-direction: column;
        background: linear-gradient(180deg, #0e1419 0%, #1a1f2e 100%);
        border-radius: 8px;
        border: 2px solid #2d3748;
        overflow: hidden;
        position: relative;
      }
      
      .sidebar::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 40px;
        background: linear-gradient(90deg, 
          transparent 0%, 
          rgba(0, 255, 255, 0.1) 20%, 
          rgba(0, 255, 255, 0.2) 50%, 
          rgba(0, 255, 255, 0.1) 80%, 
          transparent 100%);
        z-index: 1;
        pointer-events: none;
      }
      
      .code-header {
        background: linear-gradient(90deg, #1a202c 0%, #2d3748 100%);
        padding: 12px 20px;
        border-bottom: 2px solid #4a5568;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        z-index: 2;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
      }
      
      .code-title {
        font-family: 'Orbitron', monospace;
        font-weight: 700;
        font-size: 16px;
        color: #00d4ff;
        text-transform: uppercase;
        letter-spacing: 1px;
        text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
      }
      
      .status-indicators {
        display: flex;
        gap: 8px;
        align-items: center;
      }
      
      .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #00ff88;
        box-shadow: 0 0 6px #00ff88;
        animation: pulse 2s infinite;
      }
      
      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
      }
      
      .status-text {
        font-size: 12px;
        color: #a0aec0;
        font-weight: 500;
      }
      
      #vs_container {
        flex: 1;
        display: block;
        border: none;
        background: #1a1a1a;
        position: relative;
      }
      
      .deploy-section {
        background: linear-gradient(90deg, #1a202c 0%, #2d3748 100%);
        padding: 20px;
        border-top: 2px solid #4a5568;
        display: flex;
        flex-direction: column;
        gap: 12px;
        position: relative;
      }
      
      .deploy-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, #00d4ff, transparent);
      }
      
      #deploy_code {
        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        border: none;
        color: white;
        padding: 14px 28px;
        font-size: 16px;
        font-weight: 700;
        font-family: 'Orbitron', monospace;
        text-transform: uppercase;
        letter-spacing: 1px;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        box-shadow: 
          0 4px 15px rgba(255, 107, 53, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);
      }
      
      #deploy_code::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
      }
      
      #deploy_code:hover {
        transform: translateY(-2px);
        box-shadow: 
          0 6px 20px rgba(255, 107, 53, 0.4),
          inset 0 1px 0 rgba(255, 255, 255, 0.3);
      }
      
      #deploy_code:hover::before {
        left: 100%;
      }
      
      #deploy_code:active {
        transform: translateY(0);
      }
      
      .deploy-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        color: #a0aec0;
      }
      
      .hotkey {
        background: rgba(0, 212, 255, 0.1);
        border: 1px solid rgba(0, 212, 255, 0.3);
        padding: 4px 8px;
        border-radius: 4px;
        font-family: 'JetBrains Mono', monospace;
        font-size: 11px;
        color: #00d4ff;
      }
      
      /* Custom scrollbar for the editor */
      ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      
      ::-webkit-scrollbar-track {
        background: #1a1a1a;
      }
      
      ::-webkit-scrollbar-thumb {
        background: #4a5568;
        border-radius: 4px;
      }
      
      ::-webkit-scrollbar-thumb:hover {
        background: #00d4ff;
      }
      
      /* Game-like glow effects */
      .sidebar {
        box-shadow: 
          0 0 30px rgba(0, 212, 255, 0.1),
          inset 0 0 30px rgba(0, 0, 0, 0.3);
      }
      
      canvas {
        animation: canvasGlow 3s ease-in-out infinite alternate;
      }
      
      @keyframes canvasGlow {
        from {
          box-shadow: 
            0 0 20px rgba(0, 255, 255, 0.1),
            inset 0 0 20px rgba(0, 0, 0, 0.5);
        }
        to {
          box-shadow: 
            0 0 30px rgba(0, 255, 255, 0.2),
            inset 0 0 20px rgba(0, 0, 0, 0.5);
        }
      }
      
      /* Responsive adjustments */
      @media (max-width: 1200px) {
        .code-title {
          font-size: 14px;
        }
        
        #deploy_code {
          padding: 12px 24px;
          font-size: 14px;
        }
      }
    </style>
  </head>
  <body>
    <div class="screen_container">
        <div class="sidebar">
            <div class="code-header">
                <div class="code-title">Turret AI Controller</div>
                <div class="status-indicators">
                    <div class="status-dot"></div>
                    <span class="status-text">READY</span>
                </div>
            </div>
            <div id="vs_container"></div>
            <div class="deploy-section">
                <button id="deploy_code">Deploy Code</button>
                <div class="deploy-info">
                    <span>Compile and execute turret logic</span>
                    <span class="hotkey">Ctrl + Enter</span>
                </div>
            </div>
        </div>
        <div class="game-section">
            <canvas id="renderCanvas"></canvas>
            <div class="radar-container">
                <div class="radar-header">
                    <div class="radar-title">Tactical Radar</div>
                    <div class="radar-info">
                        <span>Range: <span id="radar-range">10km</span></span>
                        <span>RPM: <span id="radar-rpm">20</span></span>
                        <span>Contacts: <span id="contact-count">0</span></span>
                        <span>Status: <span id="radar-status">ACTIVE</span></span>
                    </div>
                </div>
                <div class="radar-display">
                    <div class="radar-screen" id="radar-screen">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
    <script src="../node_modules/monaco-editor/min/vs/loader.js"></script>
    <script>
        require.config({
            paths: { vs: "https://cdn.jsdelivr.net/npm/monaco-editor@0.27.0/min/vs" }
        });
        require.config({ paths: { vs: '../node_modules/monaco-editor/min/vs' } });
        
        const baseCode = ['\tconsole.log("hello!")'].join('\n');

        require(['vs/editor/editor.main'], function () {
            var editor = monaco.editor.create(document.getElementById('vs_container'), {
                value: [baseCode].join('\n'),
                language: 'javascript',
                theme: 'vs-dark',
                automaticLayout: true,
                fontSize: 14,
                lineNumbers: 'on',
                roundedSelection: false,
                scrollBeyondLastLine: false,
                readOnly: false,
                minimap: {
                    enabled: true
                }
            });

            window.userCode = baseCode;

            editor.getModel().onDidChangeContent(() => {
                window.userCode = editor.getValue();
            });
            
            // Add keyboard shortcut for deploy
            editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.Enter, function() {
                document.getElementById('deploy_code').click();
            });
        });
        
        // Example usage - you can call these from your backend:
        // window.radarSystem.updateContacts([
        //     { bearing: 45, range: 3.5, type: 'hostile' },
        //     { bearing: 180, range: 7.2, type: 'friendly' },
        //     { bearing: 270, range: 2.1, type: 'neutral' }
        // ]);
        
        // Demo: Add some sample contacts (remove this in production)
        // setTimeout(() => {
        //     window.radarSystem.updateContacts([
        //         { bearing: 30, range: 2.5, type: 'hostile' },
        //         { bearing: 120, range: 6.0, type: 'hostile' },
        //         { bearing: 200, range: 4.2, type: 'friendly' },
        //         { bearing: 315, range: 8.5, type: 'neutral' }
        //     ]);
        // }, 1000);
    </script>
  </body>
</html>