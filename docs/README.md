# Code Defense Documentation

This directory contains the complete documentation for the Code Defense tower defense game, built with Sphinx and designed for ReadTheDocs hosting.

## Documentation Structure

```
docs/
├── source/
│   ├── index.rst              # Main documentation homepage
│   ├── tutorial/              # Step-by-step learning guides
│   │   ├── index.rst
│   │   ├── getting-started.rst
│   │   └── basic-turret-control.rst
│   ├── api/                   # Complete API reference
│   │   ├── index.rst
│   │   └── turret-api.rst
│   ├── examples/              # Code examples and patterns
│   │   └── index.rst
│   ├── game-mechanics/        # Game engine details
│   │   └── index.rst
│   ├── development/           # Developer guides
│   │   └── index.rst
│   ├── reference/             # Quick reference materials
│   │   └── index.rst
│   ├── _static/
│   │   └── custom.css         # Custom styling
│   └── conf.py                # Sphinx configuration
├── requirements.txt           # Python dependencies
├── Makefile                   # Build automation
└── README.md                  # This file
```

## Building Documentation Locally

### Prerequisites

- Python 3.11+
- pip package manager

### Setup

1. **Install dependencies:**
   ```bash
   cd docs
   pip install -r requirements.txt
   ```

2. **Build HTML documentation:**
   ```bash
   make html
   ```

3. **View documentation:**
   Open `build/html/index.html` in your browser

### Other Build Formats

```bash
# Build PDF (requires LaTeX)
make latexpdf

# Build ePub
make epub

# Clean build files
make clean
```

## ReadTheDocs Integration

This documentation is configured for automatic building on ReadTheDocs:

- **Configuration:** `.readthedocs.yaml` in project root
- **Python requirements:** `docs/requirements.txt`
- **Build tool:** Sphinx with custom theme
- **Formats:** HTML, PDF, ePub

### Features

- **Custom styling** with game-themed colors and fonts
- **Mermaid diagrams** for architecture visualization
- **Code syntax highlighting** with copy buttons
- **Responsive design** for mobile and desktop
- **Search functionality** across all content
- **Cross-references** between sections

## Content Guidelines

### Writing Style

- **Clear and concise** explanations
- **Code examples** for every concept
- **Step-by-step tutorials** for learning
- **Complete API documentation** with parameters and return values

### Code Examples

All code examples should be:
- **Functional** and tested
- **Well-commented** for clarity
- **Progressive** in complexity
- **Relevant** to real game scenarios

### Documentation Standards

- Use **reStructuredText** (.rst) format
- Follow **Sphinx conventions** for cross-references
- Include **docstrings** in source code
- Maintain **consistent formatting**

## Contributing to Documentation

### Adding New Content

1. **Create new .rst files** in appropriate directories
2. **Update index.rst** files to include new content
3. **Test builds locally** before submitting
4. **Follow existing style** and structure

### Updating API Documentation

1. **Update source code** docstrings first
2. **Regenerate API docs** if using autodoc
3. **Add examples** for new features
4. **Update cross-references**

### Style Guide

- **Headers:** Use consistent hierarchy (=, -, ~, ^)
- **Code blocks:** Always specify language
- **Links:** Use meaningful link text
- **Images:** Include alt text and captions

## Maintenance

### Regular Updates

- **Keep examples current** with latest game version
- **Update API docs** when code changes
- **Fix broken links** and references
- **Improve clarity** based on user feedback

### Version Management

- **Tag releases** for stable documentation versions
- **Maintain compatibility** with game versions
- **Archive old versions** when necessary

## Troubleshooting

### Common Build Issues

**Missing dependencies:**
```bash
pip install -r requirements.txt
```

**Sphinx errors:**
```bash
make clean
make html
```

**Theme issues:**
Check `conf.py` theme configuration and CSS files.

### ReadTheDocs Issues

- Verify `.readthedocs.yaml` configuration
- Check build logs on ReadTheDocs dashboard
- Ensure all dependencies are in `requirements.txt`

## Resources

- [Sphinx Documentation](https://www.sphinx-doc.org/)
- [ReadTheDocs Guide](https://docs.readthedocs.io/)
- [reStructuredText Primer](https://www.sphinx-doc.org/en/master/usage/restructuredtext/basics.html)
- [Sphinx RTD Theme](https://sphinx-rtd-theme.readthedocs.io/)

For questions or issues with the documentation, please open an issue in the main project repository.
