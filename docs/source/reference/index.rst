Reference
=========

Quick reference materials and lookup tables for Code Defense development.

.. toctree::
   :maxdepth: 2
   :caption: Reference Sections:

   coordinate-systems
   constants-and-limits
   error-codes
   keyboard-shortcuts
   glossary

Quick Reference Tables
----------------------

**Coordinate System Summary**

+------------------+-------------------+---------------------------+
| System           | Range             | Description               |
+==================+===================+===========================+
| Heading          | 0° - 360°         | 0°=North, 90°=East        |
+------------------+-------------------+---------------------------+
| Elevation        | -45° - +85°       | 0°=Horizontal, +90°=Up    |
+------------------+-------------------+---------------------------+
| Bearing          | 0° - 360°         | Same as heading           |
+------------------+-------------------+---------------------------+
| Range            | 0 - 10000m        | Distance in meters        |
+------------------+-------------------+---------------------------+

**Turret Specifications**

+------------------+-------------------+---------------------------+
| Parameter        | Default Value     | Description               |
+==================+===================+===========================+
| Rotation Speed   | 10°/sec           | Heading change rate       |
+------------------+-------------------+---------------------------+
| Elevation Speed  | 8°/sec            | Elevation change rate     |
+------------------+-------------------+---------------------------+
| Muzzle Velocity  | 600 m/s           | Projectile initial speed  |
+------------------+-------------------+---------------------------+
| Reload Time      | 1.5 seconds       | Time between shots        |
+------------------+-------------------+---------------------------+
| Barrel Length    | 2 meters          | Distance from pivot       |
+------------------+-------------------+---------------------------+

**Radar Specifications**

+------------------+-------------------+---------------------------+
| Parameter        | Default Value     | Description               |
+==================+===================+===========================+
| Maximum Range    | 10000m            | Detection limit           |
+------------------+-------------------+---------------------------+
| Beam Width       | 3°                | Angular resolution        |
+------------------+-------------------+---------------------------+
| Rotation Speed   | 20 RPM            | Beam rotation rate        |
+------------------+-------------------+---------------------------+
| Update Rate      | 60 Hz             | Data refresh frequency    |
+------------------+-------------------+---------------------------+

**Physics Constants**

+------------------+-------------------+---------------------------+
| Constant         | Value             | Description               |
+==================+===================+===========================+
| Gravity          | 9.81 m/s²         | Downward acceleration     |
+------------------+-------------------+---------------------------+
| Air Resistance   | Minimal           | Simplified model          |
+------------------+-------------------+---------------------------+
| Frame Rate       | 60 FPS            | Game loop frequency       |
+------------------+-------------------+---------------------------+
| Time Step        | 16.67ms           | Physics update interval   |
+------------------+-------------------+---------------------------+

Common Calculations
-------------------

**Distance Formula**

.. code-block:: javascript

   function distance(x1, y1, x2, y2) {
       return Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
   }

**Angle Between Points**

.. code-block:: javascript

   function angleBetween(x1, y1, x2, y2) {
       return Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;
   }

**Normalize Angle (0-360)**

.. code-block:: javascript

   function normalizeAngle(angle) {
       while (angle < 0) angle += 360;
       while (angle >= 360) angle -= 360;
       return angle;
   }

**Degrees to Radians**

.. code-block:: javascript

   function toRadians(degrees) {
       return degrees * Math.PI / 180;
   }

**Radians to Degrees**

.. code-block:: javascript

   function toDegrees(radians) {
       return radians * 180 / Math.PI;
   }

**Projectile Time of Flight**

.. code-block:: javascript

   function timeOfFlight(range, elevation, muzzleVelocity) {
       const g = 9.81;
       const angle = elevation * Math.PI / 180;
       const vx = muzzleVelocity * Math.cos(angle);
       const vy = muzzleVelocity * Math.sin(angle);
       
       // Simplified calculation (flat ground)
       return (2 * vy) / g;
   }

**Lead Angle Calculation**

.. code-block:: javascript

   function calculateLead(targetBearing, targetRange, targetSpeed, targetHeading, muzzleVelocity) {
       const timeToTarget = targetRange / muzzleVelocity;
       const targetTravel = targetSpeed * timeToTarget;
       const angularTravel = (targetTravel / targetRange) * (180 / Math.PI);
       
       // Adjust for target heading relative to bearing
       const relativeHeading = targetHeading - targetBearing;
       const leadAngle = angularTravel * Math.cos(relativeHeading * Math.PI / 180);
       
       return targetBearing + leadAngle;
   }

Error Codes and Messages
------------------------

**Common Error Messages**

- ``"Turret elevation out of range"`` - Elevation beyond -45° to +85°
- ``"Invalid heading value"`` - Heading not a valid number
- ``"Radar contact not found"`` - Requested contact ID doesn't exist
- ``"Turret is reloading"`` - Attempted to fire during reload period
- ``"Scene not initialized"`` - Game engine not ready

**Debugging Tips**

.. code-block:: javascript

   // Check turret status
   console.log("Turret Status:", {
       heading: turret.getCurrentHeading(),
       elevation: turret.getCurrentElevation(),
       onTarget: turret.isOnTarget(),
       moving: turret.isMoving()
   });
   
   // Monitor radar contacts
   const contacts = radar.getContacts();
   console.log(`${contacts.length} contacts detected`);
   contacts.forEach((contact, i) => {
       console.log(`Contact ${i}: ${contact.bearing}° at ${contact.range}m`);
   });
   
   // Performance monitoring
   const startTime = performance.now();
   // ... your code here
   const duration = performance.now() - startTime;
   if (duration > 10) {
       console.warn(`Slow code detected: ${duration.toFixed(2)}ms`);
   }

Keyboard Shortcuts
------------------

**Game Interface**
   - ``Ctrl + Enter`` - Deploy/Execute code
   - ``F11`` - Toggle fullscreen
   - ``Esc`` - Pause game

**Code Editor**
   - ``Ctrl + S`` - Save code (auto-saved)
   - ``Ctrl + Z`` - Undo
   - ``Ctrl + Y`` - Redo
   - ``Ctrl + F`` - Find
   - ``Ctrl + H`` - Find and replace
   - ``F12`` - Open browser developer tools

**Browser Developer Tools**
   - ``F12`` - Open/close developer tools
   - ``Ctrl + Shift + C`` - Inspect element
   - ``Ctrl + Shift + J`` - Open console
   - ``Ctrl + Shift + I`` - Open developer tools

Mathematical Formulas
---------------------

**Ballistics**

Range formula: ``R = (v² × sin(2θ)) / g``

Time of flight: ``t = (2v × sin(θ)) / g``

Maximum height: ``h = (v² × sin²(θ)) / (2g)``

**Trigonometry**

.. code-block:: javascript

   // Convert polar to cartesian
   x = range * Math.cos(bearing * Math.PI / 180);
   y = range * Math.sin(bearing * Math.PI / 180);
   
   // Convert cartesian to polar
   range = Math.sqrt(x * x + y * y);
   bearing = Math.atan2(y, x) * 180 / Math.PI;

**Vector Math**

.. code-block:: javascript

   // Vector addition
   resultX = vector1X + vector2X;
   resultY = vector1Y + vector2Y;
   
   // Vector magnitude
   magnitude = Math.sqrt(x * x + y * y);
   
   // Unit vector
   unitX = x / magnitude;
   unitY = y / magnitude;

This reference section provides quick access to the most commonly needed information while developing Code Defense programs.
