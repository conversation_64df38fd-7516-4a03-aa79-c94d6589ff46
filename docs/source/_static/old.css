/* Custom CSS for Code Defense Documentation */

/* Import fonts */
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;700&family=Orbitron:wght@400;700;900&display=swap');

/* Custom color scheme inspired by the game */
:root {
    --primary-color: #00ff00;
    --secondary-color: #00d4ff;
    --accent-color: #ff6b35;
    --dark-bg: #0f0f23;
    --darker-bg: #1a1a2e;
    --text-light: #e8e8e8;
    --text-muted: #a0aec0;
}

/* Code blocks styling */
.highlight {
    background: var(--darker-bg) !important;
    border: 1px solid var(--primary-color);
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.1);
}

.highlight pre {
    font-family: 'JetBrains Mono', monospace !important;
    color: var(--text-light) !important;
}

/* Headers with game-like styling */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Orbitron', monospace !important;
    color: var(--primary-color) !important;
    text-shadow: 0 0 5px rgba(0, 255, 0, 0.3);
}

/* Navigation styling */
.wy-nav-side {
    background: linear-gradient(180deg, var(--dark-bg) 0%, var(--darker-bg) 100%) !important;
}

.wy-menu-vertical a {
    color: var(--text-light) !important;
}

.wy-menu-vertical a:hover {
    background-color: rgba(0, 255, 0, 0.1) !important;
    color: var(--primary-color) !important;
}

/* Content area */
.wy-nav-content {
    background: var(--dark-bg) !important;
    color: var(--text-light) !important;
}

/* Admonitions */
.admonition {
    border-left: 4px solid var(--primary-color) !important;
    background: rgba(0, 255, 0, 0.05) !important;
}

.admonition-title {
    background: rgba(0, 255, 0, 0.1) !important;
    color: var(--primary-color) !important;
    font-family: 'Orbitron', monospace !important;
}

/* Tables */
.wy-table-responsive table td, .wy-table-responsive table th {
    background: var(--darker-bg) !important;
    color: var(--text-light) !important;
    border-color: var(--primary-color) !important;
}

/* Links */
a {
    color: var(--secondary-color) !important;
}

a:hover {
    color: var(--primary-color) !important;
}

/* Search box */
.wy-side-nav-search {
    background: var(--darker-bg) !important;
}

.wy-side-nav-search input[type=text] {
    background: var(--dark-bg) !important;
    color: var(--text-light) !important;
    border: 1px solid var(--primary-color) !important;
}

/* Copy button styling */
.copybtn {
    background: var(--accent-color) !important;
    color: white !important;
    border: none !important;
    font-family: 'JetBrains Mono', monospace !important;
}

.copybtn:hover {
    background: var(--primary-color) !important;
    color: black !important;
}
