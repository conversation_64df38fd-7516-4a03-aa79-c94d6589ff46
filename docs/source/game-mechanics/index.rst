Game Mechanics
===============

Understanding the underlying game mechanics will help you write more effective defense programs.

.. toctree::
   :maxdepth: 2
   :caption: Mechanics Sections:

   physics-simulation
   ballistics
   radar-simulation
   enemy-behavior
   scoring-system

Core Systems
------------

**Physics Engine**
   The game uses Babylon.js physics simulation with realistic ballistics, gravity, and collision detection.

**Radar System**
   Simulates realistic radar scanning with beam rotation, range limitations, and contact tracking.

**Enemy AI**
   Dynamic enemy behavior with varying speeds, paths, and threat levels.

**Ballistics Model**
   Accurate projectile physics including gravity, air resistance, and trajectory calculation.

Key Parameters
--------------

**Turret Specifications**
   - Rotation speed: 10-30 degrees/second (configurable)
   - Elevation range: -45° to +85°
   - Muzzle velocity: 600 m/s (typical)
   - Reload time: ~1-2 seconds between shots

**Radar Specifications**
   - Maximum range: 10,000 meters
   - Beam width: 2-5 degrees
   - Rotation speed: 10-30 RPM
   - Update rate: 60 Hz

**Physics Constants**
   - Gravity: 9.81 m/s² downward
   - Air resistance: Minimal (simplified model)
   - Collision detection: High precision

**Enemy Characteristics**
   - Speed range: 5-50 m/s
   - Size: 1-5 meter radius
   - Health: 1-3 hits to destroy
   - Behavior: Straight line, evasive, or curved paths

Coordinate Systems
------------------

**World Coordinates**
   - Origin at turret base
   - X-axis: East (+) / West (-)
   - Z-axis: North (+) / South (-)
   - Y-axis: Up (+) / Down (-)

**Polar Coordinates**
   - Bearing: 0° = North, 90° = East, 180° = South, 270° = West
   - Range: Distance in meters from turret
   - Elevation: Angle above/below horizon

**Turret Coordinates**
   - Heading: Horizontal rotation (0-360°)
   - Elevation: Vertical angle (-45° to +85°)

Timing and Performance
----------------------

**Game Loop**
   - Runs at 60 FPS (16.67ms per frame)
   - Your code executes once per frame
   - Physics updates happen between code executions

**Performance Guidelines**
   - Keep calculations simple and efficient
   - Avoid complex loops over large datasets
   - Cache expensive calculations when possible
   - Use early returns to skip unnecessary work

**Real-time Constraints**
   - Code must complete within frame time budget
   - Long-running calculations will cause frame drops
   - Consider spreading complex work across multiple frames

Ballistics Calculations
-----------------------

**Basic Trajectory**
   For a projectile fired at angle θ with velocity v:
   
   - Time of flight: t = (2v sin θ) / g
   - Maximum range: R = v² sin(2θ) / g
   - Maximum height: h = v² sin²(θ) / (2g)

**Predictive Targeting**
   To hit a moving target:
   
   1. Calculate time for projectile to reach target position
   2. Predict target's future position based on current velocity
   3. Aim at predicted intercept point
   4. Account for projectile drop due to gravity

**Example Calculation**

.. code-block:: javascript

   function calculateInterceptAngle(targetBearing, targetRange, targetSpeed, targetHeading) {
       const muzzleVel = turret.getMuzzleVelocity();
       
       // Time for projectile to reach current target position
       const timeToTarget = targetRange / muzzleVel;
       
       // Distance target will travel in that time
       const targetTravel = targetSpeed * timeToTarget;
       
       // Angular displacement (simplified)
       const angularDisplacement = (targetTravel / targetRange) * (180 / Math.PI);
       
       // Adjust bearing based on target heading
       const leadAngle = angularDisplacement * Math.cos((targetHeading - targetBearing) * Math.PI / 180);
       
       return targetBearing + leadAngle;
   }

Radar Mechanics
---------------

**Beam Scanning**
   - Radar beam rotates continuously
   - Contacts only detected when beam passes over them
   - Detection probability varies with range and target size

**Contact Tracking**
   - Contacts fade after beam passes (realistic behavior)
   - Track correlation maintains contact identity
   - Position updates only when beam revisits target

**Range and Bearing Accuracy**
   - Range accuracy: ±10 meters at long range
   - Bearing accuracy: ±1 degree
   - Update rate depends on beam rotation speed

Scoring and Objectives
----------------------

**Primary Objectives**
   - Prevent enemies from reaching the base
   - Minimize ammunition expenditure
   - Maximize hit probability

**Scoring Factors**
   - Successful hits: +100 points
   - Enemy destroyed: +500 points
   - Enemy reaches base: -1000 points
   - Ammunition efficiency bonus
   - Time-based multipliers

**Performance Metrics**
   - Hit rate percentage
   - Ammunition efficiency
   - Response time to new threats
   - Multi-target handling capability

Understanding these mechanics will help you design more effective defense algorithms and optimize your code for better performance.
