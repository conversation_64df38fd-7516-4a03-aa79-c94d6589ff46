Code Defense Documentation
===========================

.. image:: https://img.shields.io/badge/License-MIT-yellow.svg
   :target: https://opensource.org/licenses/MIT
   :alt: License: MIT

.. image:: https://img.shields.io/badge/Engine-Babylon.js-blue
   :target: https://www.babylonjs.com/
   :alt: Engine: Babylon.js

.. image:: https://img.shields.io/badge/Language-JavaScript-yellow
   :target: https://developer.mozilla.org/en-US/docs/Web/JavaScript
   :alt: Language: JavaScript

**Code Defense** is an innovative educational game that combines programming education with military simulation. Players learn real programming concepts by writing code to control various weapons systems, from basic turrets to advanced guided missile systems, while defending against increasingly sophisticated threats.

.. note::
   This is an educational game designed to teach programming concepts through engaging military simulation scenarios. All content is for educational purposes only.

Features
--------

🎯 **Strategic Programming**
   Write JavaScript code to control advanced artillery turrets, program targeting algorithms, and implement tactical responses.

🛡️ **Defensive Warfare**
   Protect your base from waves of enemies using programmable defense systems with increasing complexity.

📡 **Radar Systems**
   Utilize realistic radar tracking and target acquisition systems with sophisticated tracking algorithms.

⚡ **Real-time Combat**
   Experience physics-based ballistics and real-time 3D combat in a realistic military simulation environment.

Quick Start
-----------

1. **Play the Game**: Visit the `landing page <../../../index.html>`_ to start playing
2. **Read the Tutorial**: Check out our :doc:`tutorial/getting-started` guide
3. **Explore the API**: Learn about the :doc:`api/index` for advanced programming
4. **Study Examples**: Browse :doc:`examples/index` for code samples

.. toctree::
   :maxdepth: 2
   :caption: Contents:

   tutorial/index
   api/index
   examples/index
   game-mechanics/index
   development/index
   reference/index

Educational Focus
-----------------

* **Programming Fundamentals**: Variables, functions, loops, conditionals
* **Mathematics**: Trigonometry, ballistics, vector math, numerical methods  
* **Physics**: Projectile motion, guidance systems, sensor limitations
* **Systems Engineering**: Multi-system coordination, resource allocation
* **Military Tactics**: Threat assessment, engagement doctrine, defensive strategy

Current Implementation (v0.1)
------------------------------

✅ 3D Babylon.js environment with realistic physics
✅ Programmable turret system with elevation/azimuth control
✅ Basic enemy AI and spawning system
✅ Ballistic projectile simulation with gravity
✅ Real-time code execution and debugging
✅ Collision detection and explosion effects
✅ Radar system with contact tracking
✅ Monaco code editor integration
✅ Real-time tactical display

Planned Features
----------------

🔄 **Phase 1: Basic Systems (Levels 1-6)**
   - Enhanced turret control and ballistics
   - Multiple enemy types and behaviors
   - Advanced radar and tracking systems

🔄 **Phase 2: Advanced Weapons (Levels 7-12)**
   - Guided missile systems
   - Multi-target engagement
   - Electronic warfare capabilities

🔄 **Phase 3: Defensive Systems (Levels 13-18)**
   - Point defense systems
   - Layered defense coordination
   - Battle management systems

Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
