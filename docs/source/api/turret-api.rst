Turret API
==========

The Turret API provides complete control over your defensive turret system, including aiming, firing, and status monitoring.

Core Methods
------------

aimTo(heading, elevation)
~~~~~~~~~~~~~~~~~~~~~~~~~

Aims the turret to the specified heading and elevation.

**Parameters:**
   - ``heading`` (number): Target heading in degrees (0-360)
   - ``elevation`` (number): Target elevation in degrees (-45 to +85)

**Returns:**
   - ``void``

**Example:**

.. code-block:: javascript

   // Aim northeast at 30 degrees elevation
   turret.aimTo(45, 30);
   
   // Aim due south, horizontal
   turret.aimTo(180, 0);

fire()
~~~~~~

Fires a projectile from the current turret position.

**Returns:**
   - ``boolean``: ``true`` if shot was fired, ``false`` if turret is reloading

**Example:**

.. code-block:: javascript

   if (turret.isOnTarget()) {
       const fired = turret.fire();
       if (fired) {
           console.log("Shot fired!");
       } else {
           console.log("Turret is reloading...");
       }
   }

stop()
~~~~~~

Immediately stops all turret movement.

**Returns:**
   - ``void``

**Example:**

.. code-block:: javascript

   // Emergency stop
   turret.stop();

Status Methods
--------------

isOnTarget()
~~~~~~~~~~~~

Checks if the turret has reached its target position and is ready to fire accurately.

**Returns:**
   - ``boolean``: ``true`` if turret is on target, ``false`` if still moving

**Example:**

.. code-block:: javascript

   turret.aimTo(90, 45);
   
   // Wait for turret to reach position
   if (turret.isOnTarget()) {
       turret.fire();
   }

isMoving()
~~~~~~~~~~

Checks if the turret is currently moving (rotating or elevating).

**Returns:**
   - ``boolean``: ``true`` if turret is moving, ``false`` if stationary

**Example:**

.. code-block:: javascript

   if (!turret.isMoving()) {
       console.log("Turret is stationary");
   }

getCurrentHeading()
~~~~~~~~~~~~~~~~~~~

Gets the current heading of the turret.

**Returns:**
   - ``number``: Current heading in degrees (0-360)

**Example:**

.. code-block:: javascript

   const heading = turret.getCurrentHeading();
   console.log(`Turret facing: ${heading}°`);

getCurrentElevation()
~~~~~~~~~~~~~~~~~~~~~

Gets the current elevation of the turret barrel.

**Returns:**
   - ``number``: Current elevation in degrees (-45 to +85)

**Example:**

.. code-block:: javascript

   const elevation = turret.getCurrentElevation();
   console.log(`Barrel elevation: ${elevation}°`);

getTargetHeading()
~~~~~~~~~~~~~~~~~~

Gets the target heading the turret is moving toward.

**Returns:**
   - ``number``: Target heading in degrees (0-360)

**Example:**

.. code-block:: javascript

   turret.aimTo(180, 30);
   console.log(`Moving to heading: ${turret.getTargetHeading()}°`);

getTargetElevation()
~~~~~~~~~~~~~~~~~~~~

Gets the target elevation the turret is moving toward.

**Returns:**
   - ``number``: Target elevation in degrees (-45 to +85)

**Example:**

.. code-block:: javascript

   turret.aimTo(180, 30);
   console.log(`Moving to elevation: ${turret.getTargetElevation()}°`);

Configuration Methods
---------------------

setRotationSpeed(headingSpeed, elevationSpeed)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Sets the rotation speed for turret movement.

**Parameters:**
   - ``headingSpeed`` (number): Heading rotation speed in degrees/second
   - ``elevationSpeed`` (number, optional): Elevation speed in degrees/second

**Returns:**
   - ``void``

**Example:**

.. code-block:: javascript

   // Set faster rotation for quick response
   turret.setRotationSpeed(30, 25);
   
   // Set only heading speed (elevation unchanged)
   turret.setRotationSpeed(45);

getMuzzleVelocity()
~~~~~~~~~~~~~~~~~~~

Gets the muzzle velocity of projectiles fired by this turret.

**Returns:**
   - ``number``: Muzzle velocity in meters/second

**Example:**

.. code-block:: javascript

   const velocity = turret.getMuzzleVelocity();
   console.log(`Projectile speed: ${velocity} m/s`);

getElevationLimits()
~~~~~~~~~~~~~~~~~~~~

Gets the minimum and maximum elevation limits for the turret.

**Returns:**
   - ``object``: ``{min: number, max: number}`` with elevation limits in degrees

**Example:**

.. code-block:: javascript

   const limits = turret.getElevationLimits();
   console.log(`Elevation range: ${limits.min}° to ${limits.max}°`);

Advanced Usage
--------------

Smooth vs. Instant Aiming
~~~~~~~~~~~~~~~~~~~~~~~~~~

By default, ``aimTo()`` moves the turret smoothly. For instant positioning (useful for testing):

.. code-block:: javascript

   // This is handled internally - all aiming is smooth for realism

Predictive Targeting
~~~~~~~~~~~~~~~~~~~~

Combine turret control with radar data for predictive targeting:

.. code-block:: javascript

   const contacts = radar.getContacts();
   for (let contact of contacts) {
       // Calculate lead angle based on target movement
       const leadTime = contact.range / turret.getMuzzleVelocity();
       const futureHeading = contact.bearing + (contact.heading * leadTime);
       
       turret.aimTo(futureHeading, 30);
       if (turret.isOnTarget()) {
           turret.fire();
       }
   }

Error Handling
--------------

The turret API handles invalid inputs gracefully:

- Headings outside 0-360° are normalized
- Elevations are clamped to valid range
- Null/undefined values use current position
- Invalid speeds are ignored

.. code-block:: javascript

   // These are all handled safely
   turret.aimTo(450, 30);    // Normalized to 90°
   turret.aimTo(90, 100);    // Clamped to max elevation
   turret.aimTo(null, 30);   // Uses current heading
