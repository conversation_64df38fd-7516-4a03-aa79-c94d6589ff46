API Reference
=============

This section provides detailed documentation for all available APIs in Code Defense.

.. toctree::
   :maxdepth: 2
   :caption: API Sections:

   turret-api
   radar-api
   game-api
   utilities

Overview
--------

The Code Defense API provides several main interfaces:

**Turret API**
   Control your defensive turret systems with precise aiming and firing capabilities.

**Radar API**
   Access sensor data, track targets, and process contact information.

**Game API**
   Interact with the game environment, access timing information, and manage resources.

**Utilities**
   Helper functions for mathematics, coordinate conversions, and common calculations.

Global Objects
--------------

When your code runs, several global objects are available:

.. code-block:: javascript

   // Main turret control interface
   turret.aimTo(heading, elevation);
   turret.fire();
   
   // Radar system access
   const contacts = radar.getContacts();
   
   // Game environment
   const deltaTime = game.getDeltaTime();

API Conventions
---------------

**Coordinate Systems**
   - Angles in degrees (0-360 for heading, -90 to +90 for elevation)
   - Distances in meters
   - Time in seconds

**Return Values**
   - Boolean functions return ``true``/``false``
   - Numeric functions return numbers or ``null`` if invalid
   - Array functions return empty arrays if no data

**Error Handling**
   - Invalid parameters are clamped to valid ranges
   - Null/undefined inputs default to safe values
   - Console warnings for debugging

Quick Reference
---------------

**Essential Turret Commands**

.. code-block:: javascript

   // Aiming and firing
   turret.aimTo(heading, elevation)     // Aim turret
   turret.fire()                        // Fire projectile
   turret.stop()                        // Stop all movement
   
   // Status queries
   turret.isOnTarget()                  // Ready to fire?
   turret.isMoving()                    // Currently moving?
   turret.getCurrentHeading()           // Current heading
   turret.getCurrentElevation()         // Current elevation

**Essential Radar Commands**

.. code-block:: javascript

   // Contact information
   radar.getContacts()                  // All detected contacts
   radar.getTrackedContacts()           // Actively tracked targets
   radar.getContactById(id)             // Specific contact details
   
   // Radar status
   radar.getRange()                     // Maximum detection range
   radar.getBeamBearing()               // Current beam direction

**Essential Game Commands**

.. code-block:: javascript

   // Timing and environment
   game.getDeltaTime()                  // Time since last frame
   game.getTotalTime()                  // Total elapsed time
   game.isPaused()                      // Game pause state
