Getting Started
===============

Welcome to Code Defense! This guide will help you understand the game interface and write your first defense program.

Game Interface Overview
-----------------------

The game interface consists of several key components:

**Code Editor (Left Panel)**
   - Monaco editor with JavaScript syntax highlighting
   - Real-time error checking and autocomplete
   - Deploy button to execute your code

**3D Game View (Top Right)**
   - Real-time 3D battlefield visualization
   - Your turret, enemies, and projectiles
   - Physics-based simulation

**Radar Display (Bottom Right)**
   - Circular radar screen showing detected contacts
   - Range rings and bearing indicators
   - Real-time target tracking information

Your First Program
------------------

Let's start with a simple program that makes your turret rotate:

.. code-block:: javascript

   // Your first turret control program
   console.log("Defense system online!");
   
   // Aim the turret to 45 degrees heading, 30 degrees elevation
   turret.aimTo(45, 30);
   
   // Wait for the turret to reach position, then fire
   if (turret.isOnTarget()) {
       turret.fire();
       console.log("Shot fired!");
   }

Understanding the Code
----------------------

Let's break down what this code does:

1. **Console Output**: ``console.log()`` prints messages to help you debug
2. **Turret Control**: ``turret.aimTo(heading, elevation)`` aims your turret
3. **Status Check**: ``turret.isOnTarget()`` returns true when aiming is complete
4. **Firing**: ``turret.fire()`` shoots a projectile

Key Concepts
------------

**Coordinate System**
   - Heading: 0° = North, 90° = East, 180° = South, 270° = West
   - Elevation: 0° = horizontal, 90° = straight up, negative values = below horizon

**Turret Limitations**
   - Rotation speed: Limited degrees per second
   - Elevation limits: Typically -45° to +85°
   - Reload time: Brief delay between shots

**Game Loop**
   Your code runs continuously in a game loop, so you can:
   - Check for new targets
   - Update turret position
   - Make firing decisions

Next Steps
----------

Try these exercises to get familiar with the interface:

1. **Experiment with Aiming**
   
   .. code-block:: javascript
   
      // Try different headings and elevations
      turret.aimTo(90, 45);   // East, 45° up
      turret.aimTo(180, 0);   // South, horizontal
      turret.aimTo(270, 60);  // West, 60° up

2. **Add Status Monitoring**
   
   .. code-block:: javascript
   
      // Monitor turret status
      console.log("Current heading:", turret.getCurrentHeading());
      console.log("Current elevation:", turret.getCurrentElevation());
      console.log("Is moving:", turret.isMoving());

3. **Simple Firing Pattern**
   
   .. code-block:: javascript
   
      // Fire at different positions in sequence
      const targets = [
          {heading: 0, elevation: 30},
          {heading: 90, elevation: 30},
          {heading: 180, elevation: 30},
          {heading: 270, elevation: 30}
      ];
      
      // This is a simplified example - you'll learn better patterns later
      for (let target of targets) {
          turret.aimTo(target.heading, target.elevation);
          if (turret.isOnTarget()) {
              turret.fire();
          }
      }

Common Mistakes
---------------

**Firing Too Fast**
   Don't call ``turret.fire()`` every frame - the turret needs time to reload.

**Not Checking Status**
   Always check ``turret.isOnTarget()`` before firing for accuracy.

**Ignoring Limits**
   Respect elevation limits - extreme angles may not be reachable.

What's Next?
------------

Now that you understand the basics, move on to :doc:`basic-turret-control` to learn more sophisticated control techniques.
