Basic Turret Control
====================

Now that you understand the game interface, let's dive deeper into controlling your turret effectively.

Understanding Turret Movement
-----------------------------

Your turret has two axes of movement:

**Heading (Horizontal Rotation)**
   - 0° = North, 90° = East, 180° = South, 270° = West
   - Continuous rotation (can go beyond 360°)
   - Limited by rotation speed (typically 10°/second)

**Elevation (Vertical Angle)**
   - 0° = Horizontal, positive = upward, negative = downward
   - Limited range: typically -45° to +85°
   - Limited by elevation speed (typically 8°/second)

Basic Movement Commands
-----------------------

**Simple Aiming**

.. code-block:: javascript

   // Aim to specific heading and elevation
   turret.aimTo(90, 30);  // East, 30 degrees up
   
   // Check if turret has reached target position
   if (turret.isOnTarget()) {
       console.log("Turret is on target!");
       turret.fire();
   }

**Monitoring Movement**

.. code-block:: javascript

   // Get current position
   const currentHeading = turret.getCurrentHeading();
   const currentElevation = turret.getCurrentElevation();
   
   console.log(`Turret at: ${currentHeading}°, ${currentElevation}°`);
   
   // Check if turret is moving
   if (turret.isMoving()) {
       console.log("Turret is moving to target position");
   }

**Emergency Stop**

.. code-block:: javascript

   // Stop all turret movement immediately
   turret.stop();

Smooth vs. Instant Movement
---------------------------

The turret always moves smoothly to simulate realistic mechanical systems. This means:

- Movement takes time based on distance and speed
- You must wait for movement to complete before firing accurately
- Use ``turret.isOnTarget()`` to check when ready

**Example: Waiting for Position**

.. code-block:: javascript

   // Set target position
   turret.aimTo(180, 45);
   
   // Wait for turret to reach position
   if (turret.isOnTarget()) {
       turret.fire();
       console.log("Fired at target position");
   } else {
       console.log("Still moving to target...");
   }

Advanced Movement Techniques
----------------------------

**Predictive Positioning**

.. code-block:: javascript

   // Get target information from radar
   const contacts = radar.getContacts();
   
   if (contacts.length > 0) {
       const target = contacts[0];
       
       // Aim at current target position
       turret.aimTo(target.bearing, 30);
       
       // Only fire when turret is precisely aimed
       if (turret.isOnTarget()) {
           turret.fire();
           console.log(`Engaging target at ${target.bearing}°`);
       }
   }

**Sector Scanning**

.. code-block:: javascript

   // Scan a specific sector for threats
   const scanStart = 315;  // Northwest
   const scanEnd = 45;     // Northeast
   const scanStep = 15;    // Degrees per step
   
   // Simple scanning pattern
   let currentScanAngle = scanStart;
   
   turret.aimTo(currentScanAngle, 20);
   
   if (turret.isOnTarget()) {
       // Move to next scan position
       currentScanAngle += scanStep;
       if (currentScanAngle > scanEnd) {
           currentScanAngle = scanStart;  // Reset to start
       }
   }

**Speed Control**

.. code-block:: javascript

   // Adjust turret movement speed for different situations
   
   // Fast movement for quick response
   turret.setRotationSpeed(30, 25);  // 30°/s heading, 25°/s elevation
   
   // Slower movement for precision
   turret.setRotationSpeed(5, 5);    // 5°/s for both axes
   
   // Get current speed settings
   console.log("Muzzle velocity:", turret.getMuzzleVelocity(), "m/s");

Firing Control
--------------

**Basic Firing**

.. code-block:: javascript

   // Simple fire command
   const fired = turret.fire();
   
   if (fired) {
       console.log("Shot fired!");
   } else {
       console.log("Turret is reloading...");
   }

**Controlled Firing Patterns**

.. code-block:: javascript

   // Fire only when properly aimed
   if (turret.isOnTarget() && !turret.isMoving()) {
       const success = turret.fire();
       if (success) {
           console.log("Accurate shot fired");
       }
   }

**Rapid Fire vs. Precision**

.. code-block:: javascript

   // Precision mode: wait for perfect aim
   if (turret.isOnTarget()) {
       turret.fire();
   }
   
   // Rapid response mode: fire as soon as roughly aimed
   const headingError = Math.abs(turret.getCurrentHeading() - turret.getTargetHeading());
   const elevationError = Math.abs(turret.getCurrentElevation() - turret.getTargetElevation());
   
   if (headingError < 5 && elevationError < 3) {  // Within 5° and 3°
       turret.fire();
   }

Common Patterns
---------------

**Target Tracking**

.. code-block:: javascript

   // Continuously track a moving target
   const contacts = radar.getContacts();
   
   if (contacts.length > 0) {
       const target = contacts[0];
       
       // Continuously update aim
       turret.aimTo(target.bearing, 25);
       
       // Fire when close enough to target
       const aimError = Math.abs(turret.getCurrentHeading() - target.bearing);
       if (aimError < 2) {  // Within 2 degrees
           turret.fire();
       }
   }

**Threat Response**

.. code-block:: javascript

   // Quick response to immediate threats
   const contacts = radar.getContacts();
   const closestRange = 2000;  // 2km threat threshold
   
   for (let contact of contacts) {
       if (contact.range < closestRange) {
           // Emergency engagement
           turret.aimTo(contact.bearing, 20);
           
           // Fire as soon as roughly aimed
           if (Math.abs(turret.getCurrentHeading() - contact.bearing) < 10) {
               turret.fire();
               console.log("Emergency engagement!");
               break;  // Only engage one threat at a time
           }
       }
   }

**Defensive Positioning**

.. code-block:: javascript

   // Return to defensive position when no threats
   const contacts = radar.getContacts();
   const defensiveHeading = 0;    // Face north
   const defensiveElevation = 30; // 30 degrees up
   
   if (contacts.length === 0) {
       // No threats - return to defensive position
       turret.aimTo(defensiveHeading, defensiveElevation);
   }

Troubleshooting
---------------

**Turret Won't Fire**
   - Check if ``turret.isOnTarget()`` returns true
   - Verify turret isn't in reload cycle
   - Ensure elevation is within valid range

**Slow Response**
   - Increase rotation speed with ``turret.setRotationSpeed()``
   - Use larger tolerance angles for faster engagement
   - Consider predictive aiming

**Inaccurate Shots**
   - Always wait for ``turret.isOnTarget()`` before firing
   - Account for target movement with lead calculations
   - Check elevation settings for proper trajectory

**Performance Issues**
   - Avoid calling ``turret.aimTo()`` every frame with the same values
   - Cache calculations when possible
   - Use early returns to skip unnecessary processing

Next Steps
----------

Now that you understand basic turret control, move on to :doc:`radar-systems` to learn how to detect and track targets effectively.
