
const getTarget = () => {
	if(
		hasMemory("targetEngaged")
	){
		return null;
	}
	if(hasMemory("target")) {
		return getMemory("target");
	}
	else {
		if(radarContacts.length == 0) {
			return null;
		}
		const target = {
			"heading": 128,
			"elevation": 50
		}
		setMemory("target", target);
		return target
	}
}

const turretIsOnTarget = (target) => {
	const currHeading = getCurrentHeading();
	const currElevation = getCurrentElevation();

	return currHeading == target["heading"] && currElevation == target["elevation"];
}



const target = getTarget();
if(target) {
	const onTarget = turretIsOnTarget(target);

	if(!onTarget) {
		if(!isMoving()) {
			aimTo(target["heading"], target["elevation"]);
		}
	}

	if(onTarget && !isMoving()) {
		fire();
		clearMemory("target");
		setMemory("targetEngaged", true);
	}
}
